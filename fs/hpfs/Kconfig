# SPDX-License-Identifier: GPL-2.0-only
config HPFS_FS
	tristate "OS/2 HPFS file system support"
	depends on BLOCK
	help
	  OS/2 is IBM's operating system for PC's, the same as Warp, and HPFS
	  is the file system used for organizing files on OS/2 hard disk
	  partitions. Say Y if you want to be able to read files from and
	  write files to an OS/2 HPFS partition on your hard drive. OS/2
	  floppies however are in regular MSDOS format, so you don't need this
	  option in order to be able to read them. Read
	  <file:Documentation/filesystems/hpfs.rst>.

	  To compile this file system support as a module, choose M here: the
	  module will be called hpfs.  If unsure, say N.
