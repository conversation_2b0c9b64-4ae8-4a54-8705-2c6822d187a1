# SPDX-License-Identifier: GPL-2.0-only
config FUSE_FS
	tristate "FUSE (Filesystem in Userspace) support"
	select FS_POSIX_ACL
	help
	  With FUSE it is possible to implement a fully functional filesystem
	  in a userspace program.

	  There's also a companion library: libfuse2.  This library is available
	  from the FUSE homepage:
	  <https://github.com/libfuse/>
	  although chances are your distribution already has that library
	  installed if you've installed the "fuse" package itself.

	  See <file:Documentation/filesystems/fuse.rst> for more information.
	  See <file:Documentation/Changes> for needed library/utility version.

	  If you want to develop a userspace FS, or if you want to use
	  a filesystem based on FUSE, answer Y or M.

config CUSE
	tristate "Character device in Userspace support"
	depends on FUSE_FS
	help
	  This FUSE extension allows character devices to be
	  implemented in userspace.

	  If you want to develop or use a userspace character device
	  based on CUSE, answer Y or M.

config VIRTIO_FS
	tristate "Virtio Filesystem"
	depends on FUSE_FS
	select VIRTIO
	help
	  The Virtio Filesystem allows guests to mount file systems from the
	  host.

	  If you want to share files between guests or with the host, answer Y
	  or M.

config FUSE_DAX
	bool "Virtio Filesystem Direct Host Memory Access support"
	default y
	select INTERVAL_TREE
	depends on VIRTIO_FS
	depends on FS_DAX
	depends on DAX_DRIVER
	help
	  This allows bypassing guest page cache and allows mapping host page
	  cache directly in guest address space.

	  If you want to allow mounting a Virtio Filesystem with the "dax"
	  option, answer Y.
