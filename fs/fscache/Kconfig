# SPDX-License-Identifier: GPL-2.0-only

config FSC<PERSON>H<PERSON>
	tristate "General filesystem local caching manager"
	help
	  This option enables a generic filesystem caching manager that can be
	  used by various network and other filesystems to cache data locally.
	  Different sorts of caches can be plugged in, depending on the
	  resources available.

	  See Documentation/filesystems/caching/fscache.rst for more information.

config FSCACHE_STATS
	bool "Gather statistical information on local caching"
	depends on FSCACHE && PROC_FS
	help
	  This option causes statistical information to be gathered on local
	  caching and exported through file:

		/proc/fs/fscache/stats

	  The gathering of statistics adds a certain amount of overhead to
	  execution as there are a quite a few stats gathered, and on a
	  multi-CPU system these may be on cachelines that keep bouncing
	  between CPUs.  On the other hand, the stats are very useful for
	  debugging purposes.  Saying 'Y' here is recommended.

	  See Documentation/filesystems/caching/fscache.rst for more information.

config FSCACHE_HISTOGRAM
	bool "Gather latency information on local caching"
	depends on FSCACHE && PROC_FS
	help
	  This option causes latency information to be gathered on local
	  caching and exported through file:

		/proc/fs/fscache/histogram

	  The generation of this histogram adds a certain amount of overhead to
	  execution as there are a number of points at which data is gathered,
	  and on a multi-CPU system these may be on cachelines that keep
	  bouncing between CPUs.  On the other hand, the histogram may be
	  useful for debugging purposes.  Saying 'N' here is recommended.

	  See Documentation/filesystems/caching/fscache.rst for more information.

config FSCACHE_DEBUG
	bool "Debug FS-Cache"
	depends on FSCACHE
	help
	  This permits debugging to be dynamically enabled in the local caching
	  management module.  If this is set, the debugging output may be
	  enabled by setting bits in /sys/modules/fscache/parameter/debug.

	  See Documentation/filesystems/caching/fscache.rst for more information.

config FSCACHE_OBJECT_LIST
	bool "Maintain global object list for debugging purposes"
	depends on FSCACHE && PROC_FS
	help
	  Maintain a global list of active fscache objects that can be
	  retrieved through /proc/fs/fscache/objects for debugging purposes
