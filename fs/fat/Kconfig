# SPDX-License-Identifier: GPL-2.0-only
config FAT_FS
	tristate
	select NLS
	help
	  If you want to use one of the FAT-based file systems (the MS-DOS and
	  VFAT (Windows 95) file systems), then you must say Y or M here
	  to include FAT support. You will then be able to mount partitions or
	  diskettes with FAT-based file systems and transparently access the
	  files on them, i.e. MSDOS files will look and behave just like all
	  other Unix files.

	  This FAT support is not a file system in itself, it only provides
	  the foundation for the other file systems. You will have to say Y or
	  M to at least one of "MSDOS fs support" or "VFAT fs support" in
	  order to make use of it.

	  Another way to read and write MSDOS floppies and hard drive
	  partitions from within Linux (but not transparently) is with the
	  mtools ("man mtools") program suite. You don't need to say Y here in
	  order to do that.

	  If you need to move large files on floppies between a DOS and a
	  Linux box, say Y here, mount the floppy under Linux with an MSDOS
	  file system and use GNU tar's M option. GNU tar is a program
	  available for Unix and DOS ("man tar" or "info tar").

	  The FAT support will enlarge your kernel by about 37 KB. If unsure,
	  say Y.

	  To compile this as a module, choose M here: the module will be called
	  fat.  Note that if you compile the FAT support as a module, you
	  cannot compile any of the FAT-based file systems into the kernel
	  -- they will have to be modules as well.

config MSDOS_FS
	tristate "MSDOS fs support"
	select FAT_FS
	help
	  This allows you to mount MSDOS partitions of your hard drive (unless
	  they are compressed; to access compressed MSDOS partitions under
	  Linux, you can either use the DOS emulator DOSEMU, described in the
	  DOSEMU-HOWTO, available from
	  <https://www.tldp.org/docs.html#howto>, or try dmsdosfs in
	  <ftp://ibiblio.org/pub/Linux/system/filesystems/dosfs/>. If you
	  intend to use dosemu with a non-compressed MSDOS partition, say Y
	  here) and MSDOS floppies. This means that file access becomes
	  transparent, i.e. the MSDOS files look and behave just like all
	  other Unix files.

	  If you have Windows 95 or Windows NT installed on your MSDOS
	  partitions, you should use the VFAT file system (say Y to "VFAT fs
	  support" below), or you will not be able to see the long filenames
	  generated by Windows 95 / Windows NT.

	  This option will enlarge your kernel by about 7 KB. If unsure,
	  answer Y. This will only work if you said Y to "DOS FAT fs support"
	  as well. To compile this as a module, choose M here: the module will
	  be called msdos.

config VFAT_FS
	tristate "VFAT (Windows-95) fs support"
	select FAT_FS
	help
	  This option provides support for normal Windows file systems with
	  long filenames.  That includes non-compressed FAT-based file systems
	  used by Windows 95, Windows 98, Windows NT 4.0, and the Unix
	  programs from the mtools package.

	  The VFAT support enlarges your kernel by about 10 KB and it only
	  works if you said Y to the "DOS FAT fs support" above.  Please read
	  the file <file:Documentation/filesystems/vfat.rst> for details.  If
	  unsure, say Y.

	  To compile this as a module, choose M here: the module will be called
	  vfat.

config FAT_DEFAULT_CODEPAGE
	int "Default codepage for FAT"
	depends on MSDOS_FS || VFAT_FS
	default 437
	help
	  This option should be set to the codepage of your FAT filesystems.
	  It can be overridden with the "codepage" mount option.
	  See <file:Documentation/filesystems/vfat.rst> for more information.

config FAT_DEFAULT_IOCHARSET
	string "Default iocharset for FAT"
	depends on VFAT_FS
	default "iso8859-1"
	help
	  Set this to the default input/output character set you'd
	  like FAT to use. It should probably match the character set
	  that most of your FAT filesystems use, and can be overridden
	  with the "iocharset" mount option for FAT filesystems.
	  Note that "utf8" is not recommended for FAT filesystems.
	  If unsure, you shouldn't set "utf8" here - select the next option
	  instead if you would like to use UTF-8 encoded file names by default.
	  See <file:Documentation/filesystems/vfat.rst> for more information.

	  Enable any character sets you need in File Systems/Native Language
	  Support.

config FAT_DEFAULT_UTF8
	bool "Enable FAT UTF-8 option by default"
	depends on VFAT_FS
	default n
	help
	  Set this if you would like to have "utf8" mount option set
	  by default when mounting FAT filesystems.

	  Even if you say Y here can always disable UTF-8 for
	  particular mount by adding "utf8=0" to mount options.

	  Say Y if you use UTF-8 encoding for file names, N otherwise.

	  See <file:Documentation/filesystems/vfat.rst> for more information.
