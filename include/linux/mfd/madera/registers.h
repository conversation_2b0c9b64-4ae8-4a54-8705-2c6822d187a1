/* SPDX-License-Identifier: GPL-2.0-only */
/*
 * Madera register definitions
 *
 * Copyright (C) 2015-2018 Cirrus Logic
 */

#ifndef MADERA_REGISTERS_H
#define MADERA_REGISTERS_H

/*
 * Register Addresses.
 */
#define MADERA_SOFTWARE_RESET				0x00
#define MADERA_HARDWARE_REVISION			0x01
#define MADERA_CTRL_IF_CFG_1				0x08
#define MADERA_CTRL_IF_CFG_2				0x09
#define MADERA_CTRL_IF_CFG_3				0x0A
#define MADERA_WRITE_SEQUENCER_CTRL_0			0x16
#define MADERA_WRITE_SEQUENCER_CTRL_1			0x17
#define MADERA_WRITE_SEQUENCER_CTRL_2			0x18
#define MADERA_TONE_GENERATOR_1				0x20
#define MADERA_TONE_GENERATOR_2				0x21
#define MADERA_TONE_GENERATOR_3				0x22
#define MADERA_TONE_GENERATOR_4				0x23
#define MADERA_TONE_GENERATOR_5				0x24
#define MADERA_PWM_DRIVE_1				0x30
#define MADERA_PWM_DRIVE_2				0x31
#define MADERA_PWM_DRIVE_3				0x32
#define MADERA_SEQUENCE_CONTROL				0x41
#define MADERA_SAMPLE_RATE_SEQUENCE_SELECT_1		0x61
#define MADERA_SAMPLE_RATE_SEQUENCE_SELECT_2		0x62
#define MADERA_SAMPLE_RATE_SEQUENCE_SELECT_3		0x63
#define MADERA_SAMPLE_RATE_SEQUENCE_SELECT_4		0x64
#define MADERA_ALWAYS_ON_TRIGGERS_SEQUENCE_SELECT_1	0x66
#define MADERA_ALWAYS_ON_TRIGGERS_SEQUENCE_SELECT_2	0x67
#define MADERA_HAPTICS_CONTROL_1			0x90
#define MADERA_HAPTICS_CONTROL_2			0x91
#define MADERA_HAPTICS_PHASE_1_INTENSITY		0x92
#define MADERA_HAPTICS_PHASE_1_DURATION			0x93
#define MADERA_HAPTICS_PHASE_2_INTENSITY		0x94
#define MADERA_HAPTICS_PHASE_2_DURATION			0x95
#define MADERA_HAPTICS_PHASE_3_INTENSITY		0x96
#define MADERA_HAPTICS_PHASE_3_DURATION			0x97
#define MADERA_HAPTICS_STATUS				0x98
#define MADERA_COMFORT_NOISE_GENERATOR			0xA0
#define MADERA_CLOCK_32K_1				0x100
#define MADERA_SYSTEM_CLOCK_1				0x101
#define MADERA_SAMPLE_RATE_1				0x102
#define MADERA_SAMPLE_RATE_2				0x103
#define MADERA_SAMPLE_RATE_3				0x104
#define MADERA_SAMPLE_RATE_1_STATUS			0x10A
#define MADERA_SAMPLE_RATE_2_STATUS			0x10B
#define MADERA_SAMPLE_RATE_3_STATUS			0x10C
#define MADERA_ASYNC_CLOCK_1				0x112
#define MADERA_ASYNC_SAMPLE_RATE_1			0x113
#define MADERA_ASYNC_SAMPLE_RATE_2			0x114
#define MADERA_ASYNC_SAMPLE_RATE_1_STATUS		0x11B
#define MADERA_ASYNC_SAMPLE_RATE_2_STATUS		0x11C
#define MADERA_DSP_CLOCK_1				0x120
#define MADERA_DSP_CLOCK_2				0x122
#define MADERA_OUTPUT_SYSTEM_CLOCK			0x149
#define MADERA_OUTPUT_ASYNC_CLOCK			0x14A
#define MADERA_RATE_ESTIMATOR_1				0x152
#define MADERA_RATE_ESTIMATOR_2				0x153
#define MADERA_RATE_ESTIMATOR_3				0x154
#define MADERA_RATE_ESTIMATOR_4				0x155
#define MADERA_RATE_ESTIMATOR_5				0x156
#define MADERA_FLL1_CONTROL_1				0x171
#define MADERA_FLL1_CONTROL_2				0x172
#define MADERA_FLL1_CONTROL_3				0x173
#define MADERA_FLL1_CONTROL_4				0x174
#define MADERA_FLL1_CONTROL_5				0x175
#define MADERA_FLL1_CONTROL_6				0x176
#define CS47L92_FLL1_CONTROL_7				0x177
#define CS47L92_FLL1_CONTROL_8				0x178
#define MADERA_FLL1_CONTROL_7				0x179
#define CS47L92_FLL1_CONTROL_9				0x179
#define MADERA_FLL1_EFS_2				0x17A
#define CS47L92_FLL1_CONTROL_10				0x17A
#define MADERA_FLL1_CONTROL_11				0x17B
#define MADERA_FLL1_DIGITAL_TEST_1			0x17D
#define CS47L35_FLL1_SYNCHRONISER_1			0x17F
#define CS47L35_FLL1_SYNCHRONISER_2			0x180
#define CS47L35_FLL1_SYNCHRONISER_3			0x181
#define CS47L35_FLL1_SYNCHRONISER_4			0x182
#define CS47L35_FLL1_SYNCHRONISER_5			0x183
#define CS47L35_FLL1_SYNCHRONISER_6			0x184
#define CS47L35_FLL1_SYNCHRONISER_7			0x185
#define CS47L35_FLL1_SPREAD_SPECTRUM			0x187
#define CS47L35_FLL1_GPIO_CLOCK				0x188
#define MADERA_FLL1_SYNCHRONISER_1			0x181
#define MADERA_FLL1_SYNCHRONISER_2			0x182
#define MADERA_FLL1_SYNCHRONISER_3			0x183
#define MADERA_FLL1_SYNCHRONISER_4			0x184
#define MADERA_FLL1_SYNCHRONISER_5			0x185
#define MADERA_FLL1_SYNCHRONISER_6			0x186
#define MADERA_FLL1_SYNCHRONISER_7			0x187
#define MADERA_FLL1_SPREAD_SPECTRUM			0x189
#define MADERA_FLL1_GPIO_CLOCK				0x18A
#define CS47L92_FLL1_GPIO_CLOCK				0x18E
#define MADERA_FLL2_CONTROL_1				0x191
#define MADERA_FLL2_CONTROL_2				0x192
#define MADERA_FLL2_CONTROL_3				0x193
#define MADERA_FLL2_CONTROL_4				0x194
#define MADERA_FLL2_CONTROL_5				0x195
#define MADERA_FLL2_CONTROL_6				0x196
#define CS47L92_FLL2_CONTROL_7				0x197
#define CS47L92_FLL2_CONTROL_8				0x198
#define MADERA_FLL2_CONTROL_7				0x199
#define CS47L92_FLL2_CONTROL_9				0x199
#define MADERA_FLL2_EFS_2				0x19A
#define CS47L92_FLL2_CONTROL_10				0x19A
#define MADERA_FLL2_CONTROL_11				0x19B
#define MADERA_FLL2_DIGITAL_TEST_1			0x19D
#define MADERA_FLL2_SYNCHRONISER_1			0x1A1
#define MADERA_FLL2_SYNCHRONISER_2			0x1A2
#define MADERA_FLL2_SYNCHRONISER_3			0x1A3
#define MADERA_FLL2_SYNCHRONISER_4			0x1A4
#define MADERA_FLL2_SYNCHRONISER_5			0x1A5
#define MADERA_FLL2_SYNCHRONISER_6			0x1A6
#define MADERA_FLL2_SYNCHRONISER_7			0x1A7
#define MADERA_FLL2_SPREAD_SPECTRUM			0x1A9
#define MADERA_FLL2_GPIO_CLOCK				0x1AA
#define CS47L92_FLL2_GPIO_CLOCK				0x1AE
#define MADERA_FLL3_CONTROL_1				0x1B1
#define MADERA_FLL3_CONTROL_2				0x1B2
#define MADERA_FLL3_CONTROL_3				0x1B3
#define MADERA_FLL3_CONTROL_4				0x1B4
#define MADERA_FLL3_CONTROL_5				0x1B5
#define MADERA_FLL3_CONTROL_6				0x1B6
#define MADERA_FLL3_CONTROL_7				0x1B9
#define MADERA_FLL3_SYNCHRONISER_1			0x1C1
#define MADERA_FLL3_SYNCHRONISER_2			0x1C2
#define MADERA_FLL3_SYNCHRONISER_3			0x1C3
#define MADERA_FLL3_SYNCHRONISER_4			0x1C4
#define MADERA_FLL3_SYNCHRONISER_5			0x1C5
#define MADERA_FLL3_SYNCHRONISER_6			0x1C6
#define MADERA_FLL3_SYNCHRONISER_7			0x1C7
#define MADERA_FLL3_SPREAD_SPECTRUM			0x1C9
#define MADERA_FLL3_GPIO_CLOCK				0x1CA
#define MADERA_FLLAO_CONTROL_1				0x1D1
#define MADERA_FLLAO_CONTROL_2				0x1D2
#define MADERA_FLLAO_CONTROL_3				0x1D3
#define MADERA_FLLAO_CONTROL_4				0x1D4
#define MADERA_FLLAO_CONTROL_5				0x1D5
#define MADERA_FLLAO_CONTROL_6				0x1D6
#define MADERA_FLLAO_CONTROL_7				0x1D8
#define MADERA_FLLAO_CONTROL_8				0x1DA
#define MADERA_FLLAO_CONTROL_9				0x1DB
#define MADERA_FLLAO_CONTROL_10				0x1DC
#define MADERA_FLLAO_CONTROL_11				0x1DD
#define MADERA_MIC_CHARGE_PUMP_1			0x200
#define MADERA_HP_CHARGE_PUMP_8				0x20B
#define MADERA_LDO1_CONTROL_1				0x210
#define MADERA_LDO2_CONTROL_1				0x213
#define MADERA_MIC_BIAS_CTRL_1				0x218
#define MADERA_MIC_BIAS_CTRL_2				0x219
#define MADERA_MIC_BIAS_CTRL_3				0x21A
#define MADERA_MIC_BIAS_CTRL_4				0x21B
#define MADERA_MIC_BIAS_CTRL_5				0x21C
#define MADERA_MIC_BIAS_CTRL_6				0x21E
#define MADERA_HP_CTRL_1L				0x225
#define MADERA_HP_CTRL_1R				0x226
#define MADERA_HP_CTRL_2L				0x227
#define MADERA_HP_CTRL_2R				0x228
#define MADERA_HP_CTRL_3L				0x229
#define MADERA_HP_CTRL_3R				0x22A
#define MADERA_DCS_HP1L_CONTROL				0x232
#define MADERA_DCS_HP1R_CONTROL				0x238
#define MADERA_EDRE_HP_STEREO_CONTROL			0x27E
#define MADERA_ACCESSORY_DETECT_MODE_1			0x293
#define MADERA_HEADPHONE_DETECT_0			0x299
#define MADERA_HEADPHONE_DETECT_1			0x29B
#define MADERA_HEADPHONE_DETECT_2			0x29C
#define MADERA_HEADPHONE_DETECT_3			0x29D
#define MADERA_HEADPHONE_DETECT_4			0x29E
#define MADERA_HEADPHONE_DETECT_5			0x29F
#define MADERA_MIC_DETECT_1_CONTROL_0			0x2A2
#define MADERA_MIC_DETECT_1_CONTROL_1			0x2A3
#define MADERA_MIC_DETECT_1_CONTROL_2			0x2A4
#define MADERA_MIC_DETECT_1_CONTROL_3			0x2A5
#define MADERA_MIC_DETECT_1_LEVEL_1			0x2A6
#define MADERA_MIC_DETECT_1_LEVEL_2			0x2A7
#define MADERA_MIC_DETECT_1_LEVEL_3			0x2A8
#define MADERA_MIC_DETECT_1_LEVEL_4			0x2A9
#define MADERA_MIC_DETECT_1_CONTROL_4			0x2AB
#define MADERA_MIC_DETECT_2_CONTROL_0			0x2B2
#define MADERA_MIC_DETECT_2_CONTROL_1			0x2B3
#define MADERA_MIC_DETECT_2_CONTROL_2			0x2B4
#define MADERA_MIC_DETECT_2_CONTROL_3			0x2B5
#define MADERA_MIC_DETECT_2_LEVEL_1			0x2B6
#define MADERA_MIC_DETECT_2_LEVEL_2			0x2B7
#define MADERA_MIC_DETECT_2_LEVEL_3			0x2B8
#define MADERA_MIC_DETECT_2_LEVEL_4			0x2B9
#define MADERA_MIC_DETECT_2_CONTROL_4			0x2BB
#define MADERA_MICD_CLAMP_CONTROL			0x2C6
#define MADERA_GP_SWITCH_1				0x2C8
#define MADERA_JACK_DETECT_ANALOGUE			0x2D3
#define MADERA_INPUT_ENABLES				0x300
#define MADERA_INPUT_ENABLES_STATUS			0x301
#define MADERA_INPUT_RATE				0x308
#define MADERA_INPUT_VOLUME_RAMP			0x309
#define MADERA_HPF_CONTROL				0x30C
#define MADERA_IN1L_CONTROL				0x310
#define MADERA_ADC_DIGITAL_VOLUME_1L			0x311
#define MADERA_DMIC1L_CONTROL				0x312
#define MADERA_IN1L_RATE_CONTROL			0x313
#define MADERA_IN1R_CONTROL				0x314
#define MADERA_ADC_DIGITAL_VOLUME_1R			0x315
#define MADERA_DMIC1R_CONTROL				0x316
#define MADERA_IN1R_RATE_CONTROL			0x317
#define MADERA_IN2L_CONTROL				0x318
#define MADERA_ADC_DIGITAL_VOLUME_2L			0x319
#define MADERA_DMIC2L_CONTROL				0x31A
#define MADERA_IN2L_RATE_CONTROL			0x31B
#define MADERA_IN2R_CONTROL				0x31C
#define MADERA_ADC_DIGITAL_VOLUME_2R			0x31D
#define MADERA_DMIC2R_CONTROL				0x31E
#define MADERA_IN2R_RATE_CONTROL			0x31F
#define MADERA_IN3L_CONTROL				0x320
#define MADERA_ADC_DIGITAL_VOLUME_3L			0x321
#define MADERA_DMIC3L_CONTROL				0x322
#define MADERA_IN3L_RATE_CONTROL			0x323
#define MADERA_IN3R_CONTROL				0x324
#define MADERA_ADC_DIGITAL_VOLUME_3R			0x325
#define MADERA_DMIC3R_CONTROL				0x326
#define MADERA_IN3R_RATE_CONTROL			0x327
#define MADERA_IN4L_CONTROL				0x328
#define MADERA_ADC_DIGITAL_VOLUME_4L			0x329
#define MADERA_DMIC4L_CONTROL				0x32A
#define MADERA_IN4L_RATE_CONTROL			0x32B
#define MADERA_IN4R_CONTROL				0x32C
#define MADERA_ADC_DIGITAL_VOLUME_4R			0x32D
#define MADERA_DMIC4R_CONTROL				0x32E
#define MADERA_IN4R_RATE_CONTROL			0x32F
#define MADERA_IN5L_CONTROL				0x330
#define MADERA_ADC_DIGITAL_VOLUME_5L			0x331
#define MADERA_DMIC5L_CONTROL				0x332
#define MADERA_IN5L_RATE_CONTROL			0x333
#define MADERA_IN5R_CONTROL				0x334
#define MADERA_ADC_DIGITAL_VOLUME_5R			0x335
#define MADERA_DMIC5R_CONTROL				0x336
#define MADERA_IN5R_RATE_CONTROL			0x337
#define MADERA_IN6L_CONTROL				0x338
#define MADERA_ADC_DIGITAL_VOLUME_6L			0x339
#define MADERA_DMIC6L_CONTROL				0x33A
#define MADERA_IN6R_CONTROL				0x33C
#define MADERA_ADC_DIGITAL_VOLUME_6R			0x33D
#define MADERA_DMIC6R_CONTROL				0x33E
#define CS47L15_ADC_INT_BIAS				0x3A8
#define CS47L15_PGA_BIAS_SEL				0x3C4
#define MADERA_OUTPUT_ENABLES_1				0x400
#define MADERA_OUTPUT_STATUS_1				0x401
#define MADERA_RAW_OUTPUT_STATUS_1			0x406
#define MADERA_OUTPUT_RATE_1				0x408
#define MADERA_OUTPUT_VOLUME_RAMP			0x409
#define MADERA_OUTPUT_PATH_CONFIG_1L			0x410
#define MADERA_DAC_DIGITAL_VOLUME_1L			0x411
#define MADERA_OUTPUT_PATH_CONFIG_1			0x412
#define MADERA_NOISE_GATE_SELECT_1L			0x413
#define MADERA_OUTPUT_PATH_CONFIG_1R			0x414
#define MADERA_DAC_DIGITAL_VOLUME_1R			0x415
#define MADERA_NOISE_GATE_SELECT_1R			0x417
#define MADERA_OUTPUT_PATH_CONFIG_2L			0x418
#define MADERA_DAC_DIGITAL_VOLUME_2L			0x419
#define MADERA_OUTPUT_PATH_CONFIG_2			0x41A
#define MADERA_NOISE_GATE_SELECT_2L			0x41B
#define MADERA_OUTPUT_PATH_CONFIG_2R			0x41C
#define MADERA_DAC_DIGITAL_VOLUME_2R			0x41D
#define MADERA_NOISE_GATE_SELECT_2R			0x41F
#define MADERA_OUTPUT_PATH_CONFIG_3L			0x420
#define MADERA_DAC_DIGITAL_VOLUME_3L			0x421
#define MADERA_OUTPUT_PATH_CONFIG_3			0x422
#define MADERA_NOISE_GATE_SELECT_3L			0x423
#define MADERA_OUTPUT_PATH_CONFIG_3R			0x424
#define MADERA_DAC_DIGITAL_VOLUME_3R			0x425
#define MADERA_NOISE_GATE_SELECT_3R			0x427
#define MADERA_OUTPUT_PATH_CONFIG_4L			0x428
#define MADERA_DAC_DIGITAL_VOLUME_4L			0x429
#define MADERA_NOISE_GATE_SELECT_4L			0x42B
#define MADERA_OUTPUT_PATH_CONFIG_4R			0x42C
#define MADERA_DAC_DIGITAL_VOLUME_4R			0x42D
#define MADERA_NOISE_GATE_SELECT_4R			0x42F
#define MADERA_OUTPUT_PATH_CONFIG_5L			0x430
#define MADERA_DAC_DIGITAL_VOLUME_5L			0x431
#define MADERA_NOISE_GATE_SELECT_5L			0x433
#define MADERA_OUTPUT_PATH_CONFIG_5R			0x434
#define MADERA_DAC_DIGITAL_VOLUME_5R			0x435
#define MADERA_NOISE_GATE_SELECT_5R			0x437
#define MADERA_OUTPUT_PATH_CONFIG_6L			0x438
#define MADERA_DAC_DIGITAL_VOLUME_6L			0x439
#define MADERA_NOISE_GATE_SELECT_6L			0x43B
#define MADERA_OUTPUT_PATH_CONFIG_6R			0x43C
#define MADERA_DAC_DIGITAL_VOLUME_6R			0x43D
#define MADERA_NOISE_GATE_SELECT_6R			0x43F
#define MADERA_DAC_AEC_CONTROL_1			0x450
#define MADERA_DAC_AEC_CONTROL_2			0x451
#define MADERA_NOISE_GATE_CONTROL			0x458
#define MADERA_PDM_SPK1_CTRL_1				0x490
#define MADERA_PDM_SPK1_CTRL_2				0x491
#define MADERA_PDM_SPK2_CTRL_1				0x492
#define MADERA_PDM_SPK2_CTRL_2				0x493
#define MADERA_HP1_SHORT_CIRCUIT_CTRL			0x4A0
#define MADERA_HP2_SHORT_CIRCUIT_CTRL			0x4A1
#define MADERA_HP3_SHORT_CIRCUIT_CTRL			0x4A2
#define MADERA_HP_TEST_CTRL_1				0x4A4
#define MADERA_HP_TEST_CTRL_5				0x4A8
#define MADERA_HP_TEST_CTRL_6				0x4A9
#define MADERA_AIF1_BCLK_CTRL				0x500
#define MADERA_AIF1_TX_PIN_CTRL				0x501
#define MADERA_AIF1_RX_PIN_CTRL				0x502
#define MADERA_AIF1_RATE_CTRL				0x503
#define MADERA_AIF1_FORMAT				0x504
#define MADERA_AIF1_RX_BCLK_RATE			0x506
#define MADERA_AIF1_FRAME_CTRL_1			0x507
#define MADERA_AIF1_FRAME_CTRL_2			0x508
#define MADERA_AIF1_FRAME_CTRL_3			0x509
#define MADERA_AIF1_FRAME_CTRL_4			0x50A
#define MADERA_AIF1_FRAME_CTRL_5			0x50B
#define MADERA_AIF1_FRAME_CTRL_6			0x50C
#define MADERA_AIF1_FRAME_CTRL_7			0x50D
#define MADERA_AIF1_FRAME_CTRL_8			0x50E
#define MADERA_AIF1_FRAME_CTRL_9			0x50F
#define MADERA_AIF1_FRAME_CTRL_10			0x510
#define MADERA_AIF1_FRAME_CTRL_11			0x511
#define MADERA_AIF1_FRAME_CTRL_12			0x512
#define MADERA_AIF1_FRAME_CTRL_13			0x513
#define MADERA_AIF1_FRAME_CTRL_14			0x514
#define MADERA_AIF1_FRAME_CTRL_15			0x515
#define MADERA_AIF1_FRAME_CTRL_16			0x516
#define MADERA_AIF1_FRAME_CTRL_17			0x517
#define MADERA_AIF1_FRAME_CTRL_18			0x518
#define MADERA_AIF1_TX_ENABLES				0x519
#define MADERA_AIF1_RX_ENABLES				0x51A
#define MADERA_AIF1_FORCE_WRITE				0x51B
#define MADERA_AIF2_BCLK_CTRL				0x540
#define MADERA_AIF2_TX_PIN_CTRL				0x541
#define MADERA_AIF2_RX_PIN_CTRL				0x542
#define MADERA_AIF2_RATE_CTRL				0x543
#define MADERA_AIF2_FORMAT				0x544
#define MADERA_AIF2_RX_BCLK_RATE			0x546
#define MADERA_AIF2_FRAME_CTRL_1			0x547
#define MADERA_AIF2_FRAME_CTRL_2			0x548
#define MADERA_AIF2_FRAME_CTRL_3			0x549
#define MADERA_AIF2_FRAME_CTRL_4			0x54A
#define MADERA_AIF2_FRAME_CTRL_5			0x54B
#define MADERA_AIF2_FRAME_CTRL_6			0x54C
#define MADERA_AIF2_FRAME_CTRL_7			0x54D
#define MADERA_AIF2_FRAME_CTRL_8			0x54E
#define MADERA_AIF2_FRAME_CTRL_9			0x54F
#define MADERA_AIF2_FRAME_CTRL_10			0x550
#define MADERA_AIF2_FRAME_CTRL_11			0x551
#define MADERA_AIF2_FRAME_CTRL_12			0x552
#define MADERA_AIF2_FRAME_CTRL_13			0x553
#define MADERA_AIF2_FRAME_CTRL_14			0x554
#define MADERA_AIF2_FRAME_CTRL_15			0x555
#define MADERA_AIF2_FRAME_CTRL_16			0x556
#define MADERA_AIF2_FRAME_CTRL_17			0x557
#define MADERA_AIF2_FRAME_CTRL_18			0x558
#define MADERA_AIF2_TX_ENABLES				0x559
#define MADERA_AIF2_RX_ENABLES				0x55A
#define MADERA_AIF2_FORCE_WRITE				0x55B
#define MADERA_AIF3_BCLK_CTRL				0x580
#define MADERA_AIF3_TX_PIN_CTRL				0x581
#define MADERA_AIF3_RX_PIN_CTRL				0x582
#define MADERA_AIF3_RATE_CTRL				0x583
#define MADERA_AIF3_FORMAT				0x584
#define MADERA_AIF3_RX_BCLK_RATE			0x586
#define MADERA_AIF3_FRAME_CTRL_1			0x587
#define MADERA_AIF3_FRAME_CTRL_2			0x588
#define MADERA_AIF3_FRAME_CTRL_3			0x589
#define MADERA_AIF3_FRAME_CTRL_4			0x58A
#define MADERA_AIF3_FRAME_CTRL_5			0x58B
#define MADERA_AIF3_FRAME_CTRL_6			0x58C
#define MADERA_AIF3_FRAME_CTRL_7			0x58D
#define MADERA_AIF3_FRAME_CTRL_8			0x58E
#define MADERA_AIF3_FRAME_CTRL_9			0x58F
#define MADERA_AIF3_FRAME_CTRL_10			0x590
#define MADERA_AIF3_FRAME_CTRL_11			0x591
#define MADERA_AIF3_FRAME_CTRL_12			0x592
#define MADERA_AIF3_FRAME_CTRL_13			0x593
#define MADERA_AIF3_FRAME_CTRL_14			0x594
#define MADERA_AIF3_FRAME_CTRL_15			0x595
#define MADERA_AIF3_FRAME_CTRL_16			0x596
#define MADERA_AIF3_FRAME_CTRL_17			0x597
#define MADERA_AIF3_FRAME_CTRL_18			0x598
#define MADERA_AIF3_TX_ENABLES				0x599
#define MADERA_AIF3_RX_ENABLES				0x59A
#define MADERA_AIF3_FORCE_WRITE				0x59B
#define MADERA_AIF4_BCLK_CTRL				0x5A0
#define MADERA_AIF4_TX_PIN_CTRL				0x5A1
#define MADERA_AIF4_RX_PIN_CTRL				0x5A2
#define MADERA_AIF4_RATE_CTRL				0x5A3
#define MADERA_AIF4_FORMAT				0x5A4
#define MADERA_AIF4_RX_BCLK_RATE			0x5A6
#define MADERA_AIF4_FRAME_CTRL_1			0x5A7
#define MADERA_AIF4_FRAME_CTRL_2			0x5A8
#define MADERA_AIF4_FRAME_CTRL_3			0x5A9
#define MADERA_AIF4_FRAME_CTRL_4			0x5AA
#define MADERA_AIF4_FRAME_CTRL_11			0x5B1
#define MADERA_AIF4_FRAME_CTRL_12			0x5B2
#define MADERA_AIF4_TX_ENABLES				0x5B9
#define MADERA_AIF4_RX_ENABLES				0x5BA
#define MADERA_AIF4_FORCE_WRITE				0x5BB
#define MADERA_SPD1_TX_CONTROL				0x5C2
#define MADERA_SPD1_TX_CHANNEL_STATUS_1			0x5C3
#define MADERA_SPD1_TX_CHANNEL_STATUS_2			0x5C4
#define MADERA_SPD1_TX_CHANNEL_STATUS_3			0x5C5
#define MADERA_SLIMBUS_FRAMER_REF_GEAR			0x5E3
#define MADERA_SLIMBUS_RATES_1				0x5E5
#define MADERA_SLIMBUS_RATES_2				0x5E6
#define MADERA_SLIMBUS_RATES_3				0x5E7
#define MADERA_SLIMBUS_RATES_4				0x5E8
#define MADERA_SLIMBUS_RATES_5				0x5E9
#define MADERA_SLIMBUS_RATES_6				0x5EA
#define MADERA_SLIMBUS_RATES_7				0x5EB
#define MADERA_SLIMBUS_RATES_8				0x5EC
#define MADERA_SLIMBUS_RX_CHANNEL_ENABLE		0x5F5
#define MADERA_SLIMBUS_TX_CHANNEL_ENABLE		0x5F6
#define MADERA_SLIMBUS_RX_PORT_STATUS			0x5F7
#define MADERA_SLIMBUS_TX_PORT_STATUS			0x5F8
#define MADERA_PWM1MIX_INPUT_1_SOURCE			0x640
#define MADERA_PWM1MIX_INPUT_1_VOLUME			0x641
#define MADERA_PWM1MIX_INPUT_2_SOURCE			0x642
#define MADERA_PWM1MIX_INPUT_2_VOLUME			0x643
#define MADERA_PWM1MIX_INPUT_3_SOURCE			0x644
#define MADERA_PWM1MIX_INPUT_3_VOLUME			0x645
#define MADERA_PWM1MIX_INPUT_4_SOURCE			0x646
#define MADERA_PWM1MIX_INPUT_4_VOLUME			0x647
#define MADERA_PWM2MIX_INPUT_1_SOURCE			0x648
#define MADERA_PWM2MIX_INPUT_1_VOLUME			0x649
#define MADERA_PWM2MIX_INPUT_2_SOURCE			0x64A
#define MADERA_PWM2MIX_INPUT_2_VOLUME			0x64B
#define MADERA_PWM2MIX_INPUT_3_SOURCE			0x64C
#define MADERA_PWM2MIX_INPUT_3_VOLUME			0x64D
#define MADERA_PWM2MIX_INPUT_4_SOURCE			0x64E
#define MADERA_PWM2MIX_INPUT_4_VOLUME			0x64F
#define MADERA_OUT1LMIX_INPUT_1_SOURCE			0x680
#define MADERA_OUT1LMIX_INPUT_1_VOLUME			0x681
#define MADERA_OUT1LMIX_INPUT_2_SOURCE			0x682
#define MADERA_OUT1LMIX_INPUT_2_VOLUME			0x683
#define MADERA_OUT1LMIX_INPUT_3_SOURCE			0x684
#define MADERA_OUT1LMIX_INPUT_3_VOLUME			0x685
#define MADERA_OUT1LMIX_INPUT_4_SOURCE			0x686
#define MADERA_OUT1LMIX_INPUT_4_VOLUME			0x687
#define MADERA_OUT1RMIX_INPUT_1_SOURCE			0x688
#define MADERA_OUT1RMIX_INPUT_1_VOLUME			0x689
#define MADERA_OUT1RMIX_INPUT_2_SOURCE			0x68A
#define MADERA_OUT1RMIX_INPUT_2_VOLUME			0x68B
#define MADERA_OUT1RMIX_INPUT_3_SOURCE			0x68C
#define MADERA_OUT1RMIX_INPUT_3_VOLUME			0x68D
#define MADERA_OUT1RMIX_INPUT_4_SOURCE			0x68E
#define MADERA_OUT1RMIX_INPUT_4_VOLUME			0x68F
#define MADERA_OUT2LMIX_INPUT_1_SOURCE			0x690
#define MADERA_OUT2LMIX_INPUT_1_VOLUME			0x691
#define MADERA_OUT2LMIX_INPUT_2_SOURCE			0x692
#define MADERA_OUT2LMIX_INPUT_2_VOLUME			0x693
#define MADERA_OUT2LMIX_INPUT_3_SOURCE			0x694
#define MADERA_OUT2LMIX_INPUT_3_VOLUME			0x695
#define MADERA_OUT2LMIX_INPUT_4_SOURCE			0x696
#define MADERA_OUT2LMIX_INPUT_4_VOLUME			0x697
#define MADERA_OUT2RMIX_INPUT_1_SOURCE			0x698
#define MADERA_OUT2RMIX_INPUT_1_VOLUME			0x699
#define MADERA_OUT2RMIX_INPUT_2_SOURCE			0x69A
#define MADERA_OUT2RMIX_INPUT_2_VOLUME			0x69B
#define MADERA_OUT2RMIX_INPUT_3_SOURCE			0x69C
#define MADERA_OUT2RMIX_INPUT_3_VOLUME			0x69D
#define MADERA_OUT2RMIX_INPUT_4_SOURCE			0x69E
#define MADERA_OUT2RMIX_INPUT_4_VOLUME			0x69F
#define MADERA_OUT3LMIX_INPUT_1_SOURCE			0x6A0
#define MADERA_OUT3LMIX_INPUT_1_VOLUME			0x6A1
#define MADERA_OUT3LMIX_INPUT_2_SOURCE			0x6A2
#define MADERA_OUT3LMIX_INPUT_2_VOLUME			0x6A3
#define MADERA_OUT3LMIX_INPUT_3_SOURCE			0x6A4
#define MADERA_OUT3LMIX_INPUT_3_VOLUME			0x6A5
#define MADERA_OUT3LMIX_INPUT_4_SOURCE			0x6A6
#define MADERA_OUT3LMIX_INPUT_4_VOLUME			0x6A7
#define MADERA_OUT3RMIX_INPUT_1_SOURCE			0x6A8
#define MADERA_OUT3RMIX_INPUT_1_VOLUME			0x6A9
#define MADERA_OUT3RMIX_INPUT_2_SOURCE			0x6AA
#define MADERA_OUT3RMIX_INPUT_2_VOLUME			0x6AB
#define MADERA_OUT3RMIX_INPUT_3_SOURCE			0x6AC
#define MADERA_OUT3RMIX_INPUT_3_VOLUME			0x6AD
#define MADERA_OUT3RMIX_INPUT_4_SOURCE			0x6AE
#define MADERA_OUT3RMIX_INPUT_4_VOLUME			0x6AF
#define MADERA_OUT4LMIX_INPUT_1_SOURCE			0x6B0
#define MADERA_OUT4LMIX_INPUT_1_VOLUME			0x6B1
#define MADERA_OUT4LMIX_INPUT_2_SOURCE			0x6B2
#define MADERA_OUT4LMIX_INPUT_2_VOLUME			0x6B3
#define MADERA_OUT4LMIX_INPUT_3_SOURCE			0x6B4
#define MADERA_OUT4LMIX_INPUT_3_VOLUME			0x6B5
#define MADERA_OUT4LMIX_INPUT_4_SOURCE			0x6B6
#define MADERA_OUT4LMIX_INPUT_4_VOLUME			0x6B7
#define MADERA_OUT4RMIX_INPUT_1_SOURCE			0x6B8
#define MADERA_OUT4RMIX_INPUT_1_VOLUME			0x6B9
#define MADERA_OUT4RMIX_INPUT_2_SOURCE			0x6BA
#define MADERA_OUT4RMIX_INPUT_2_VOLUME			0x6BB
#define MADERA_OUT4RMIX_INPUT_3_SOURCE			0x6BC
#define MADERA_OUT4RMIX_INPUT_3_VOLUME			0x6BD
#define MADERA_OUT4RMIX_INPUT_4_SOURCE			0x6BE
#define MADERA_OUT4RMIX_INPUT_4_VOLUME			0x6BF
#define MADERA_OUT5LMIX_INPUT_1_SOURCE			0x6C0
#define MADERA_OUT5LMIX_INPUT_1_VOLUME			0x6C1
#define MADERA_OUT5LMIX_INPUT_2_SOURCE			0x6C2
#define MADERA_OUT5LMIX_INPUT_2_VOLUME			0x6C3
#define MADERA_OUT5LMIX_INPUT_3_SOURCE			0x6C4
#define MADERA_OUT5LMIX_INPUT_3_VOLUME			0x6C5
#define MADERA_OUT5LMIX_INPUT_4_SOURCE			0x6C6
#define MADERA_OUT5LMIX_INPUT_4_VOLUME			0x6C7
#define MADERA_OUT5RMIX_INPUT_1_SOURCE			0x6C8
#define MADERA_OUT5RMIX_INPUT_1_VOLUME			0x6C9
#define MADERA_OUT5RMIX_INPUT_2_SOURCE			0x6CA
#define MADERA_OUT5RMIX_INPUT_2_VOLUME			0x6CB
#define MADERA_OUT5RMIX_INPUT_3_SOURCE			0x6CC
#define MADERA_OUT5RMIX_INPUT_3_VOLUME			0x6CD
#define MADERA_OUT5RMIX_INPUT_4_SOURCE			0x6CE
#define MADERA_OUT5RMIX_INPUT_4_VOLUME			0x6CF
#define MADERA_OUT6LMIX_INPUT_1_SOURCE			0x6D0
#define MADERA_OUT6LMIX_INPUT_1_VOLUME			0x6D1
#define MADERA_OUT6LMIX_INPUT_2_SOURCE			0x6D2
#define MADERA_OUT6LMIX_INPUT_2_VOLUME			0x6D3
#define MADERA_OUT6LMIX_INPUT_3_SOURCE			0x6D4
#define MADERA_OUT6LMIX_INPUT_3_VOLUME			0x6D5
#define MADERA_OUT6LMIX_INPUT_4_SOURCE			0x6D6
#define MADERA_OUT6LMIX_INPUT_4_VOLUME			0x6D7
#define MADERA_OUT6RMIX_INPUT_1_SOURCE			0x6D8
#define MADERA_OUT6RMIX_INPUT_1_VOLUME			0x6D9
#define MADERA_OUT6RMIX_INPUT_2_SOURCE			0x6DA
#define MADERA_OUT6RMIX_INPUT_2_VOLUME			0x6DB
#define MADERA_OUT6RMIX_INPUT_3_SOURCE			0x6DC
#define MADERA_OUT6RMIX_INPUT_3_VOLUME			0x6DD
#define MADERA_OUT6RMIX_INPUT_4_SOURCE			0x6DE
#define MADERA_OUT6RMIX_INPUT_4_VOLUME			0x6DF
#define MADERA_AIF1TX1MIX_INPUT_1_SOURCE		0x700
#define MADERA_AIF1TX1MIX_INPUT_1_VOLUME		0x701
#define MADERA_AIF1TX1MIX_INPUT_2_SOURCE		0x702
#define MADERA_AIF1TX1MIX_INPUT_2_VOLUME		0x703
#define MADERA_AIF1TX1MIX_INPUT_3_SOURCE		0x704
#define MADERA_AIF1TX1MIX_INPUT_3_VOLUME		0x705
#define MADERA_AIF1TX1MIX_INPUT_4_SOURCE		0x706
#define MADERA_AIF1TX1MIX_INPUT_4_VOLUME		0x707
#define MADERA_AIF1TX2MIX_INPUT_1_SOURCE		0x708
#define MADERA_AIF1TX2MIX_INPUT_1_VOLUME		0x709
#define MADERA_AIF1TX2MIX_INPUT_2_SOURCE		0x70A
#define MADERA_AIF1TX2MIX_INPUT_2_VOLUME		0x70B
#define MADERA_AIF1TX2MIX_INPUT_3_SOURCE		0x70C
#define MADERA_AIF1TX2MIX_INPUT_3_VOLUME		0x70D
#define MADERA_AIF1TX2MIX_INPUT_4_SOURCE		0x70E
#define MADERA_AIF1TX2MIX_INPUT_4_VOLUME		0x70F
#define MADERA_AIF1TX3MIX_INPUT_1_SOURCE		0x710
#define MADERA_AIF1TX3MIX_INPUT_1_VOLUME		0x711
#define MADERA_AIF1TX3MIX_INPUT_2_SOURCE		0x712
#define MADERA_AIF1TX3MIX_INPUT_2_VOLUME		0x713
#define MADERA_AIF1TX3MIX_INPUT_3_SOURCE		0x714
#define MADERA_AIF1TX3MIX_INPUT_3_VOLUME		0x715
#define MADERA_AIF1TX3MIX_INPUT_4_SOURCE		0x716
#define MADERA_AIF1TX3MIX_INPUT_4_VOLUME		0x717
#define MADERA_AIF1TX4MIX_INPUT_1_SOURCE		0x718
#define MADERA_AIF1TX4MIX_INPUT_1_VOLUME		0x719
#define MADERA_AIF1TX4MIX_INPUT_2_SOURCE		0x71A
#define MADERA_AIF1TX4MIX_INPUT_2_VOLUME		0x71B
#define MADERA_AIF1TX4MIX_INPUT_3_SOURCE		0x71C
#define MADERA_AIF1TX4MIX_INPUT_3_VOLUME		0x71D
#define MADERA_AIF1TX4MIX_INPUT_4_SOURCE		0x71E
#define MADERA_AIF1TX4MIX_INPUT_4_VOLUME		0x71F
#define MADERA_AIF1TX5MIX_INPUT_1_SOURCE		0x720
#define MADERA_AIF1TX5MIX_INPUT_1_VOLUME		0x721
#define MADERA_AIF1TX5MIX_INPUT_2_SOURCE		0x722
#define MADERA_AIF1TX5MIX_INPUT_2_VOLUME		0x723
#define MADERA_AIF1TX5MIX_INPUT_3_SOURCE		0x724
#define MADERA_AIF1TX5MIX_INPUT_3_VOLUME		0x725
#define MADERA_AIF1TX5MIX_INPUT_4_SOURCE		0x726
#define MADERA_AIF1TX5MIX_INPUT_4_VOLUME		0x727
#define MADERA_AIF1TX6MIX_INPUT_1_SOURCE		0x728
#define MADERA_AIF1TX6MIX_INPUT_1_VOLUME		0x729
#define MADERA_AIF1TX6MIX_INPUT_2_SOURCE		0x72A
#define MADERA_AIF1TX6MIX_INPUT_2_VOLUME		0x72B
#define MADERA_AIF1TX6MIX_INPUT_3_SOURCE		0x72C
#define MADERA_AIF1TX6MIX_INPUT_3_VOLUME		0x72D
#define MADERA_AIF1TX6MIX_INPUT_4_SOURCE		0x72E
#define MADERA_AIF1TX6MIX_INPUT_4_VOLUME		0x72F
#define MADERA_AIF1TX7MIX_INPUT_1_SOURCE		0x730
#define MADERA_AIF1TX7MIX_INPUT_1_VOLUME		0x731
#define MADERA_AIF1TX7MIX_INPUT_2_SOURCE		0x732
#define MADERA_AIF1TX7MIX_INPUT_2_VOLUME		0x733
#define MADERA_AIF1TX7MIX_INPUT_3_SOURCE		0x734
#define MADERA_AIF1TX7MIX_INPUT_3_VOLUME		0x735
#define MADERA_AIF1TX7MIX_INPUT_4_SOURCE		0x736
#define MADERA_AIF1TX7MIX_INPUT_4_VOLUME		0x737
#define MADERA_AIF1TX8MIX_INPUT_1_SOURCE		0x738
#define MADERA_AIF1TX8MIX_INPUT_1_VOLUME		0x739
#define MADERA_AIF1TX8MIX_INPUT_2_SOURCE		0x73A
#define MADERA_AIF1TX8MIX_INPUT_2_VOLUME		0x73B
#define MADERA_AIF1TX8MIX_INPUT_3_SOURCE		0x73C
#define MADERA_AIF1TX8MIX_INPUT_3_VOLUME		0x73D
#define MADERA_AIF1TX8MIX_INPUT_4_SOURCE		0x73E
#define MADERA_AIF1TX8MIX_INPUT_4_VOLUME		0x73F
#define MADERA_AIF2TX1MIX_INPUT_1_SOURCE		0x740
#define MADERA_AIF2TX1MIX_INPUT_1_VOLUME		0x741
#define MADERA_AIF2TX1MIX_INPUT_2_SOURCE		0x742
#define MADERA_AIF2TX1MIX_INPUT_2_VOLUME		0x743
#define MADERA_AIF2TX1MIX_INPUT_3_SOURCE		0x744
#define MADERA_AIF2TX1MIX_INPUT_3_VOLUME		0x745
#define MADERA_AIF2TX1MIX_INPUT_4_SOURCE		0x746
#define MADERA_AIF2TX1MIX_INPUT_4_VOLUME		0x747
#define MADERA_AIF2TX2MIX_INPUT_1_SOURCE		0x748
#define MADERA_AIF2TX2MIX_INPUT_1_VOLUME		0x749
#define MADERA_AIF2TX2MIX_INPUT_2_SOURCE		0x74A
#define MADERA_AIF2TX2MIX_INPUT_2_VOLUME		0x74B
#define MADERA_AIF2TX2MIX_INPUT_3_SOURCE		0x74C
#define MADERA_AIF2TX2MIX_INPUT_3_VOLUME		0x74D
#define MADERA_AIF2TX2MIX_INPUT_4_SOURCE		0x74E
#define MADERA_AIF2TX2MIX_INPUT_4_VOLUME		0x74F
#define MADERA_AIF2TX3MIX_INPUT_1_SOURCE		0x750
#define MADERA_AIF2TX3MIX_INPUT_1_VOLUME		0x751
#define MADERA_AIF2TX3MIX_INPUT_2_SOURCE		0x752
#define MADERA_AIF2TX3MIX_INPUT_2_VOLUME		0x753
#define MADERA_AIF2TX3MIX_INPUT_3_SOURCE		0x754
#define MADERA_AIF2TX3MIX_INPUT_3_VOLUME		0x755
#define MADERA_AIF2TX3MIX_INPUT_4_SOURCE		0x756
#define MADERA_AIF2TX3MIX_INPUT_4_VOLUME		0x757
#define MADERA_AIF2TX4MIX_INPUT_1_SOURCE		0x758
#define MADERA_AIF2TX4MIX_INPUT_1_VOLUME		0x759
#define MADERA_AIF2TX4MIX_INPUT_2_SOURCE		0x75A
#define MADERA_AIF2TX4MIX_INPUT_2_VOLUME		0x75B
#define MADERA_AIF2TX4MIX_INPUT_3_SOURCE		0x75C
#define MADERA_AIF2TX4MIX_INPUT_3_VOLUME		0x75D
#define MADERA_AIF2TX4MIX_INPUT_4_SOURCE		0x75E
#define MADERA_AIF2TX4MIX_INPUT_4_VOLUME		0x75F
#define MADERA_AIF2TX5MIX_INPUT_1_SOURCE		0x760
#define MADERA_AIF2TX5MIX_INPUT_1_VOLUME		0x761
#define MADERA_AIF2TX5MIX_INPUT_2_SOURCE		0x762
#define MADERA_AIF2TX5MIX_INPUT_2_VOLUME		0x763
#define MADERA_AIF2TX5MIX_INPUT_3_SOURCE		0x764
#define MADERA_AIF2TX5MIX_INPUT_3_VOLUME		0x765
#define MADERA_AIF2TX5MIX_INPUT_4_SOURCE		0x766
#define MADERA_AIF2TX5MIX_INPUT_4_VOLUME		0x767
#define MADERA_AIF2TX6MIX_INPUT_1_SOURCE		0x768
#define MADERA_AIF2TX6MIX_INPUT_1_VOLUME		0x769
#define MADERA_AIF2TX6MIX_INPUT_2_SOURCE		0x76A
#define MADERA_AIF2TX6MIX_INPUT_2_VOLUME		0x76B
#define MADERA_AIF2TX6MIX_INPUT_3_SOURCE		0x76C
#define MADERA_AIF2TX6MIX_INPUT_3_VOLUME		0x76D
#define MADERA_AIF2TX6MIX_INPUT_4_SOURCE		0x76E
#define MADERA_AIF2TX6MIX_INPUT_4_VOLUME		0x76F
#define MADERA_AIF2TX7MIX_INPUT_1_SOURCE		0x770
#define MADERA_AIF2TX7MIX_INPUT_1_VOLUME		0x771
#define MADERA_AIF2TX7MIX_INPUT_2_SOURCE		0x772
#define MADERA_AIF2TX7MIX_INPUT_2_VOLUME		0x773
#define MADERA_AIF2TX7MIX_INPUT_3_SOURCE		0x774
#define MADERA_AIF2TX7MIX_INPUT_3_VOLUME		0x775
#define MADERA_AIF2TX7MIX_INPUT_4_SOURCE		0x776
#define MADERA_AIF2TX7MIX_INPUT_4_VOLUME		0x777
#define MADERA_AIF2TX8MIX_INPUT_1_SOURCE		0x778
#define MADERA_AIF2TX8MIX_INPUT_1_VOLUME		0x779
#define MADERA_AIF2TX8MIX_INPUT_2_SOURCE		0x77A
#define MADERA_AIF2TX8MIX_INPUT_2_VOLUME		0x77B
#define MADERA_AIF2TX8MIX_INPUT_3_SOURCE		0x77C
#define MADERA_AIF2TX8MIX_INPUT_3_VOLUME		0x77D
#define MADERA_AIF2TX8MIX_INPUT_4_SOURCE		0x77E
#define MADERA_AIF2TX8MIX_INPUT_4_VOLUME		0x77F
#define MADERA_AIF3TX1MIX_INPUT_1_SOURCE		0x780
#define MADERA_AIF3TX1MIX_INPUT_1_VOLUME		0x781
#define MADERA_AIF3TX1MIX_INPUT_2_SOURCE		0x782
#define MADERA_AIF3TX1MIX_INPUT_2_VOLUME		0x783
#define MADERA_AIF3TX1MIX_INPUT_3_SOURCE		0x784
#define MADERA_AIF3TX1MIX_INPUT_3_VOLUME		0x785
#define MADERA_AIF3TX1MIX_INPUT_4_SOURCE		0x786
#define MADERA_AIF3TX1MIX_INPUT_4_VOLUME		0x787
#define MADERA_AIF3TX2MIX_INPUT_1_SOURCE		0x788
#define MADERA_AIF3TX2MIX_INPUT_1_VOLUME		0x789
#define MADERA_AIF3TX2MIX_INPUT_2_SOURCE		0x78A
#define MADERA_AIF3TX2MIX_INPUT_2_VOLUME		0x78B
#define MADERA_AIF3TX2MIX_INPUT_3_SOURCE		0x78C
#define MADERA_AIF3TX2MIX_INPUT_3_VOLUME		0x78D
#define MADERA_AIF3TX2MIX_INPUT_4_SOURCE		0x78E
#define MADERA_AIF3TX2MIX_INPUT_4_VOLUME		0x78F
#define MADERA_AIF3TX3MIX_INPUT_1_SOURCE		0x790
#define MADERA_AIF3TX3MIX_INPUT_1_VOLUME		0x791
#define MADERA_AIF3TX3MIX_INPUT_2_SOURCE		0x792
#define MADERA_AIF3TX3MIX_INPUT_2_VOLUME		0x793
#define MADERA_AIF3TX3MIX_INPUT_3_SOURCE		0x794
#define MADERA_AIF3TX3MIX_INPUT_3_VOLUME		0x795
#define MADERA_AIF3TX3MIX_INPUT_4_SOURCE		0x796
#define MADERA_AIF3TX3MIX_INPUT_4_VOLUME		0x797
#define MADERA_AIF3TX4MIX_INPUT_1_SOURCE		0x798
#define MADERA_AIF3TX4MIX_INPUT_1_VOLUME		0x799
#define MADERA_AIF3TX4MIX_INPUT_2_SOURCE		0x79A
#define MADERA_AIF3TX4MIX_INPUT_2_VOLUME		0x79B
#define MADERA_AIF3TX4MIX_INPUT_3_SOURCE		0x79C
#define MADERA_AIF3TX4MIX_INPUT_3_VOLUME		0x79D
#define MADERA_AIF3TX4MIX_INPUT_4_SOURCE		0x79E
#define MADERA_AIF3TX4MIX_INPUT_4_VOLUME		0x79F
#define CS47L92_AIF3TX5MIX_INPUT_1_SOURCE		0x7A0
#define CS47L92_AIF3TX5MIX_INPUT_1_VOLUME		0x7A1
#define CS47L92_AIF3TX5MIX_INPUT_2_SOURCE		0x7A2
#define CS47L92_AIF3TX5MIX_INPUT_2_VOLUME		0x7A3
#define CS47L92_AIF3TX5MIX_INPUT_3_SOURCE		0x7A4
#define CS47L92_AIF3TX5MIX_INPUT_3_VOLUME		0x7A5
#define CS47L92_AIF3TX5MIX_INPUT_4_SOURCE		0x7A6
#define CS47L92_AIF3TX5MIX_INPUT_4_VOLUME		0x7A7
#define CS47L92_AIF3TX6MIX_INPUT_1_SOURCE		0x7A8
#define CS47L92_AIF3TX6MIX_INPUT_1_VOLUME		0x7A9
#define CS47L92_AIF3TX6MIX_INPUT_2_SOURCE		0x7AA
#define CS47L92_AIF3TX6MIX_INPUT_2_VOLUME		0x7AB
#define CS47L92_AIF3TX6MIX_INPUT_3_SOURCE		0x7AC
#define CS47L92_AIF3TX6MIX_INPUT_3_VOLUME		0x7AD
#define CS47L92_AIF3TX6MIX_INPUT_4_SOURCE		0x7AE
#define CS47L92_AIF3TX6MIX_INPUT_4_VOLUME		0x7AF
#define CS47L92_AIF3TX7MIX_INPUT_1_SOURCE		0x7B0
#define CS47L92_AIF3TX7MIX_INPUT_1_VOLUME		0x7B1
#define CS47L92_AIF3TX7MIX_INPUT_2_SOURCE		0x7B2
#define CS47L92_AIF3TX7MIX_INPUT_2_VOLUME		0x7B3
#define CS47L92_AIF3TX7MIX_INPUT_3_SOURCE		0x7B4
#define CS47L92_AIF3TX7MIX_INPUT_3_VOLUME		0x7B5
#define CS47L92_AIF3TX7MIX_INPUT_4_SOURCE		0x7B6
#define CS47L92_AIF3TX7MIX_INPUT_4_VOLUME		0x7B7
#define CS47L92_AIF3TX8MIX_INPUT_1_SOURCE		0x7B8
#define CS47L92_AIF3TX8MIX_INPUT_1_VOLUME		0x7B9
#define CS47L92_AIF3TX8MIX_INPUT_2_SOURCE		0x7BA
#define CS47L92_AIF3TX8MIX_INPUT_2_VOLUME		0x7BB
#define CS47L92_AIF3TX8MIX_INPUT_3_SOURCE		0x7BC
#define CS47L92_AIF3TX8MIX_INPUT_3_VOLUME		0x7BD
#define CS47L92_AIF3TX8MIX_INPUT_4_SOURCE		0x7BE
#define CS47L92_AIF3TX8MIX_INPUT_4_VOLUME		0x7BF
#define MADERA_AIF4TX1MIX_INPUT_1_SOURCE		0x7A0
#define MADERA_AIF4TX1MIX_INPUT_1_VOLUME		0x7A1
#define MADERA_AIF4TX1MIX_INPUT_2_SOURCE		0x7A2
#define MADERA_AIF4TX1MIX_INPUT_2_VOLUME		0x7A3
#define MADERA_AIF4TX1MIX_INPUT_3_SOURCE		0x7A4
#define MADERA_AIF4TX1MIX_INPUT_3_VOLUME		0x7A5
#define MADERA_AIF4TX1MIX_INPUT_4_SOURCE		0x7A6
#define MADERA_AIF4TX1MIX_INPUT_4_VOLUME		0x7A7
#define MADERA_AIF4TX2MIX_INPUT_1_SOURCE		0x7A8
#define MADERA_AIF4TX2MIX_INPUT_1_VOLUME		0x7A9
#define MADERA_AIF4TX2MIX_INPUT_2_SOURCE		0x7AA
#define MADERA_AIF4TX2MIX_INPUT_2_VOLUME		0x7AB
#define MADERA_AIF4TX2MIX_INPUT_3_SOURCE		0x7AC
#define MADERA_AIF4TX2MIX_INPUT_3_VOLUME		0x7AD
#define MADERA_AIF4TX2MIX_INPUT_4_SOURCE		0x7AE
#define MADERA_AIF4TX2MIX_INPUT_4_VOLUME		0x7AF
#define MADERA_SLIMTX1MIX_INPUT_1_SOURCE		0x7C0
#define MADERA_SLIMTX1MIX_INPUT_1_VOLUME		0x7C1
#define MADERA_SLIMTX1MIX_INPUT_2_SOURCE		0x7C2
#define MADERA_SLIMTX1MIX_INPUT_2_VOLUME		0x7C3
#define MADERA_SLIMTX1MIX_INPUT_3_SOURCE		0x7C4
#define MADERA_SLIMTX1MIX_INPUT_3_VOLUME		0x7C5
#define MADERA_SLIMTX1MIX_INPUT_4_SOURCE		0x7C6
#define MADERA_SLIMTX1MIX_INPUT_4_VOLUME		0x7C7
#define MADERA_SLIMTX2MIX_INPUT_1_SOURCE		0x7C8
#define MADERA_SLIMTX2MIX_INPUT_1_VOLUME		0x7C9
#define MADERA_SLIMTX2MIX_INPUT_2_SOURCE		0x7CA
#define MADERA_SLIMTX2MIX_INPUT_2_VOLUME		0x7CB
#define MADERA_SLIMTX2MIX_INPUT_3_SOURCE		0x7CC
#define MADERA_SLIMTX2MIX_INPUT_3_VOLUME		0x7CD
#define MADERA_SLIMTX2MIX_INPUT_4_SOURCE		0x7CE
#define MADERA_SLIMTX2MIX_INPUT_4_VOLUME		0x7CF
#define MADERA_SLIMTX3MIX_INPUT_1_SOURCE		0x7D0
#define MADERA_SLIMTX3MIX_INPUT_1_VOLUME		0x7D1
#define MADERA_SLIMTX3MIX_INPUT_2_SOURCE		0x7D2
#define MADERA_SLIMTX3MIX_INPUT_2_VOLUME		0x7D3
#define MADERA_SLIMTX3MIX_INPUT_3_SOURCE		0x7D4
#define MADERA_SLIMTX3MIX_INPUT_3_VOLUME		0x7D5
#define MADERA_SLIMTX3MIX_INPUT_4_SOURCE		0x7D6
#define MADERA_SLIMTX3MIX_INPUT_4_VOLUME		0x7D7
#define MADERA_SLIMTX4MIX_INPUT_1_SOURCE		0x7D8
#define MADERA_SLIMTX4MIX_INPUT_1_VOLUME		0x7D9
#define MADERA_SLIMTX4MIX_INPUT_2_SOURCE		0x7DA
#define MADERA_SLIMTX4MIX_INPUT_2_VOLUME		0x7DB
#define MADERA_SLIMTX4MIX_INPUT_3_SOURCE		0x7DC
#define MADERA_SLIMTX4MIX_INPUT_3_VOLUME		0x7DD
#define MADERA_SLIMTX4MIX_INPUT_4_SOURCE		0x7DE
#define MADERA_SLIMTX4MIX_INPUT_4_VOLUME		0x7DF
#define MADERA_SLIMTX5MIX_INPUT_1_SOURCE		0x7E0
#define MADERA_SLIMTX5MIX_INPUT_1_VOLUME		0x7E1
#define MADERA_SLIMTX5MIX_INPUT_2_SOURCE		0x7E2
#define MADERA_SLIMTX5MIX_INPUT_2_VOLUME		0x7E3
#define MADERA_SLIMTX5MIX_INPUT_3_SOURCE		0x7E4
#define MADERA_SLIMTX5MIX_INPUT_3_VOLUME		0x7E5
#define MADERA_SLIMTX5MIX_INPUT_4_SOURCE		0x7E6
#define MADERA_SLIMTX5MIX_INPUT_4_VOLUME		0x7E7
#define MADERA_SLIMTX6MIX_INPUT_1_SOURCE		0x7E8
#define MADERA_SLIMTX6MIX_INPUT_1_VOLUME		0x7E9
#define MADERA_SLIMTX6MIX_INPUT_2_SOURCE		0x7EA
#define MADERA_SLIMTX6MIX_INPUT_2_VOLUME		0x7EB
#define MADERA_SLIMTX6MIX_INPUT_3_SOURCE		0x7EC
#define MADERA_SLIMTX6MIX_INPUT_3_VOLUME		0x7ED
#define MADERA_SLIMTX6MIX_INPUT_4_SOURCE		0x7EE
#define MADERA_SLIMTX6MIX_INPUT_4_VOLUME		0x7EF
#define MADERA_SLIMTX7MIX_INPUT_1_SOURCE		0x7F0
#define MADERA_SLIMTX7MIX_INPUT_1_VOLUME		0x7F1
#define MADERA_SLIMTX7MIX_INPUT_2_SOURCE		0x7F2
#define MADERA_SLIMTX7MIX_INPUT_2_VOLUME		0x7F3
#define MADERA_SLIMTX7MIX_INPUT_3_SOURCE		0x7F4
#define MADERA_SLIMTX7MIX_INPUT_3_VOLUME		0x7F5
#define MADERA_SLIMTX7MIX_INPUT_4_SOURCE		0x7F6
#define MADERA_SLIMTX7MIX_INPUT_4_VOLUME		0x7F7
#define MADERA_SLIMTX8MIX_INPUT_1_SOURCE		0x7F8
#define MADERA_SLIMTX8MIX_INPUT_1_VOLUME		0x7F9
#define MADERA_SLIMTX8MIX_INPUT_2_SOURCE		0x7FA
#define MADERA_SLIMTX8MIX_INPUT_2_VOLUME		0x7FB
#define MADERA_SLIMTX8MIX_INPUT_3_SOURCE		0x7FC
#define MADERA_SLIMTX8MIX_INPUT_3_VOLUME		0x7FD
#define MADERA_SLIMTX8MIX_INPUT_4_SOURCE		0x7FE
#define MADERA_SLIMTX8MIX_INPUT_4_VOLUME		0x7FF
#define MADERA_SPDIF1TX1MIX_INPUT_1_SOURCE		0x800
#define MADERA_SPDIF1TX1MIX_INPUT_1_VOLUME		0x801
#define MADERA_SPDIF1TX2MIX_INPUT_1_SOURCE		0x808
#define MADERA_SPDIF1TX2MIX_INPUT_1_VOLUME		0x809
#define MADERA_EQ1MIX_INPUT_1_SOURCE			0x880
#define MADERA_EQ1MIX_INPUT_1_VOLUME			0x881
#define MADERA_EQ1MIX_INPUT_2_SOURCE			0x882
#define MADERA_EQ1MIX_INPUT_2_VOLUME			0x883
#define MADERA_EQ1MIX_INPUT_3_SOURCE			0x884
#define MADERA_EQ1MIX_INPUT_3_VOLUME			0x885
#define MADERA_EQ1MIX_INPUT_4_SOURCE			0x886
#define MADERA_EQ1MIX_INPUT_4_VOLUME			0x887
#define MADERA_EQ2MIX_INPUT_1_SOURCE			0x888
#define MADERA_EQ2MIX_INPUT_1_VOLUME			0x889
#define MADERA_EQ2MIX_INPUT_2_SOURCE			0x88A
#define MADERA_EQ2MIX_INPUT_2_VOLUME			0x88B
#define MADERA_EQ2MIX_INPUT_3_SOURCE			0x88C
#define MADERA_EQ2MIX_INPUT_3_VOLUME			0x88D
#define MADERA_EQ2MIX_INPUT_4_SOURCE			0x88E
#define MADERA_EQ2MIX_INPUT_4_VOLUME			0x88F
#define MADERA_EQ3MIX_INPUT_1_SOURCE			0x890
#define MADERA_EQ3MIX_INPUT_1_VOLUME			0x891
#define MADERA_EQ3MIX_INPUT_2_SOURCE			0x892
#define MADERA_EQ3MIX_INPUT_2_VOLUME			0x893
#define MADERA_EQ3MIX_INPUT_3_SOURCE			0x894
#define MADERA_EQ3MIX_INPUT_3_VOLUME			0x895
#define MADERA_EQ3MIX_INPUT_4_SOURCE			0x896
#define MADERA_EQ3MIX_INPUT_4_VOLUME			0x897
#define MADERA_EQ4MIX_INPUT_1_SOURCE			0x898
#define MADERA_EQ4MIX_INPUT_1_VOLUME			0x899
#define MADERA_EQ4MIX_INPUT_2_SOURCE			0x89A
#define MADERA_EQ4MIX_INPUT_2_VOLUME			0x89B
#define MADERA_EQ4MIX_INPUT_3_SOURCE			0x89C
#define MADERA_EQ4MIX_INPUT_3_VOLUME			0x89D
#define MADERA_EQ4MIX_INPUT_4_SOURCE			0x89E
#define MADERA_EQ4MIX_INPUT_4_VOLUME			0x89F
#define MADERA_DRC1LMIX_INPUT_1_SOURCE			0x8C0
#define MADERA_DRC1LMIX_INPUT_1_VOLUME			0x8C1
#define MADERA_DRC1LMIX_INPUT_2_SOURCE			0x8C2
#define MADERA_DRC1LMIX_INPUT_2_VOLUME			0x8C3
#define MADERA_DRC1LMIX_INPUT_3_SOURCE			0x8C4
#define MADERA_DRC1LMIX_INPUT_3_VOLUME			0x8C5
#define MADERA_DRC1LMIX_INPUT_4_SOURCE			0x8C6
#define MADERA_DRC1LMIX_INPUT_4_VOLUME			0x8C7
#define MADERA_DRC1RMIX_INPUT_1_SOURCE			0x8C8
#define MADERA_DRC1RMIX_INPUT_1_VOLUME			0x8C9
#define MADERA_DRC1RMIX_INPUT_2_SOURCE			0x8CA
#define MADERA_DRC1RMIX_INPUT_2_VOLUME			0x8CB
#define MADERA_DRC1RMIX_INPUT_3_SOURCE			0x8CC
#define MADERA_DRC1RMIX_INPUT_3_VOLUME			0x8CD
#define MADERA_DRC1RMIX_INPUT_4_SOURCE			0x8CE
#define MADERA_DRC1RMIX_INPUT_4_VOLUME			0x8CF
#define MADERA_DRC2LMIX_INPUT_1_SOURCE			0x8D0
#define MADERA_DRC2LMIX_INPUT_1_VOLUME			0x8D1
#define MADERA_DRC2LMIX_INPUT_2_SOURCE			0x8D2
#define MADERA_DRC2LMIX_INPUT_2_VOLUME			0x8D3
#define MADERA_DRC2LMIX_INPUT_3_SOURCE			0x8D4
#define MADERA_DRC2LMIX_INPUT_3_VOLUME			0x8D5
#define MADERA_DRC2LMIX_INPUT_4_SOURCE			0x8D6
#define MADERA_DRC2LMIX_INPUT_4_VOLUME			0x8D7
#define MADERA_DRC2RMIX_INPUT_1_SOURCE			0x8D8
#define MADERA_DRC2RMIX_INPUT_1_VOLUME			0x8D9
#define MADERA_DRC2RMIX_INPUT_2_SOURCE			0x8DA
#define MADERA_DRC2RMIX_INPUT_2_VOLUME			0x8DB
#define MADERA_DRC2RMIX_INPUT_3_SOURCE			0x8DC
#define MADERA_DRC2RMIX_INPUT_3_VOLUME			0x8DD
#define MADERA_DRC2RMIX_INPUT_4_SOURCE			0x8DE
#define MADERA_DRC2RMIX_INPUT_4_VOLUME			0x8DF
#define MADERA_HPLP1MIX_INPUT_1_SOURCE			0x900
#define MADERA_HPLP1MIX_INPUT_1_VOLUME			0x901
#define MADERA_HPLP1MIX_INPUT_2_SOURCE			0x902
#define MADERA_HPLP1MIX_INPUT_2_VOLUME			0x903
#define MADERA_HPLP1MIX_INPUT_3_SOURCE			0x904
#define MADERA_HPLP1MIX_INPUT_3_VOLUME			0x905
#define MADERA_HPLP1MIX_INPUT_4_SOURCE			0x906
#define MADERA_HPLP1MIX_INPUT_4_VOLUME			0x907
#define MADERA_HPLP2MIX_INPUT_1_SOURCE			0x908
#define MADERA_HPLP2MIX_INPUT_1_VOLUME			0x909
#define MADERA_HPLP2MIX_INPUT_2_SOURCE			0x90A
#define MADERA_HPLP2MIX_INPUT_2_VOLUME			0x90B
#define MADERA_HPLP2MIX_INPUT_3_SOURCE			0x90C
#define MADERA_HPLP2MIX_INPUT_3_VOLUME			0x90D
#define MADERA_HPLP2MIX_INPUT_4_SOURCE			0x90E
#define MADERA_HPLP2MIX_INPUT_4_VOLUME			0x90F
#define MADERA_HPLP3MIX_INPUT_1_SOURCE			0x910
#define MADERA_HPLP3MIX_INPUT_1_VOLUME			0x911
#define MADERA_HPLP3MIX_INPUT_2_SOURCE			0x912
#define MADERA_HPLP3MIX_INPUT_2_VOLUME			0x913
#define MADERA_HPLP3MIX_INPUT_3_SOURCE			0x914
#define MADERA_HPLP3MIX_INPUT_3_VOLUME			0x915
#define MADERA_HPLP3MIX_INPUT_4_SOURCE			0x916
#define MADERA_HPLP3MIX_INPUT_4_VOLUME			0x917
#define MADERA_HPLP4MIX_INPUT_1_SOURCE			0x918
#define MADERA_HPLP4MIX_INPUT_1_VOLUME			0x919
#define MADERA_HPLP4MIX_INPUT_2_SOURCE			0x91A
#define MADERA_HPLP4MIX_INPUT_2_VOLUME			0x91B
#define MADERA_HPLP4MIX_INPUT_3_SOURCE			0x91C
#define MADERA_HPLP4MIX_INPUT_3_VOLUME			0x91D
#define MADERA_HPLP4MIX_INPUT_4_SOURCE			0x91E
#define MADERA_HPLP4MIX_INPUT_4_VOLUME			0x91F
#define MADERA_DSP1LMIX_INPUT_1_SOURCE			0x940
#define MADERA_DSP1LMIX_INPUT_1_VOLUME			0x941
#define MADERA_DSP1LMIX_INPUT_2_SOURCE			0x942
#define MADERA_DSP1LMIX_INPUT_2_VOLUME			0x943
#define MADERA_DSP1LMIX_INPUT_3_SOURCE			0x944
#define MADERA_DSP1LMIX_INPUT_3_VOLUME			0x945
#define MADERA_DSP1LMIX_INPUT_4_SOURCE			0x946
#define MADERA_DSP1LMIX_INPUT_4_VOLUME			0x947
#define MADERA_DSP1RMIX_INPUT_1_SOURCE			0x948
#define MADERA_DSP1RMIX_INPUT_1_VOLUME			0x949
#define MADERA_DSP1RMIX_INPUT_2_SOURCE			0x94A
#define MADERA_DSP1RMIX_INPUT_2_VOLUME			0x94B
#define MADERA_DSP1RMIX_INPUT_3_SOURCE			0x94C
#define MADERA_DSP1RMIX_INPUT_3_VOLUME			0x94D
#define MADERA_DSP1RMIX_INPUT_4_SOURCE			0x94E
#define MADERA_DSP1RMIX_INPUT_4_VOLUME			0x94F
#define MADERA_DSP1AUX1MIX_INPUT_1_SOURCE		0x950
#define MADERA_DSP1AUX2MIX_INPUT_1_SOURCE		0x958
#define MADERA_DSP1AUX3MIX_INPUT_1_SOURCE		0x960
#define MADERA_DSP1AUX4MIX_INPUT_1_SOURCE		0x968
#define MADERA_DSP1AUX5MIX_INPUT_1_SOURCE		0x970
#define MADERA_DSP1AUX6MIX_INPUT_1_SOURCE		0x978
#define MADERA_DSP2LMIX_INPUT_1_SOURCE			0x980
#define MADERA_DSP2LMIX_INPUT_1_VOLUME			0x981
#define MADERA_DSP2LMIX_INPUT_2_SOURCE			0x982
#define MADERA_DSP2LMIX_INPUT_2_VOLUME			0x983
#define MADERA_DSP2LMIX_INPUT_3_SOURCE			0x984
#define MADERA_DSP2LMIX_INPUT_3_VOLUME			0x985
#define MADERA_DSP2LMIX_INPUT_4_SOURCE			0x986
#define MADERA_DSP2LMIX_INPUT_4_VOLUME			0x987
#define MADERA_DSP2RMIX_INPUT_1_SOURCE			0x988
#define MADERA_DSP2RMIX_INPUT_1_VOLUME			0x989
#define MADERA_DSP2RMIX_INPUT_2_SOURCE			0x98A
#define MADERA_DSP2RMIX_INPUT_2_VOLUME			0x98B
#define MADERA_DSP2RMIX_INPUT_3_SOURCE			0x98C
#define MADERA_DSP2RMIX_INPUT_3_VOLUME			0x98D
#define MADERA_DSP2RMIX_INPUT_4_SOURCE			0x98E
#define MADERA_DSP2RMIX_INPUT_4_VOLUME			0x98F
#define MADERA_DSP2AUX1MIX_INPUT_1_SOURCE		0x990
#define MADERA_DSP2AUX2MIX_INPUT_1_SOURCE		0x998
#define MADERA_DSP2AUX3MIX_INPUT_1_SOURCE		0x9A0
#define MADERA_DSP2AUX4MIX_INPUT_1_SOURCE		0x9A8
#define MADERA_DSP2AUX5MIX_INPUT_1_SOURCE		0x9B0
#define MADERA_DSP2AUX6MIX_INPUT_1_SOURCE		0x9B8
#define MADERA_DSP3LMIX_INPUT_1_SOURCE			0x9C0
#define MADERA_DSP3LMIX_INPUT_1_VOLUME			0x9C1
#define MADERA_DSP3LMIX_INPUT_2_SOURCE			0x9C2
#define MADERA_DSP3LMIX_INPUT_2_VOLUME			0x9C3
#define MADERA_DSP3LMIX_INPUT_3_SOURCE			0x9C4
#define MADERA_DSP3LMIX_INPUT_3_VOLUME			0x9C5
#define MADERA_DSP3LMIX_INPUT_4_SOURCE			0x9C6
#define MADERA_DSP3LMIX_INPUT_4_VOLUME			0x9C7
#define MADERA_DSP3RMIX_INPUT_1_SOURCE			0x9C8
#define MADERA_DSP3RMIX_INPUT_1_VOLUME			0x9C9
#define MADERA_DSP3RMIX_INPUT_2_SOURCE			0x9CA
#define MADERA_DSP3RMIX_INPUT_2_VOLUME			0x9CB
#define MADERA_DSP3RMIX_INPUT_3_SOURCE			0x9CC
#define MADERA_DSP3RMIX_INPUT_3_VOLUME			0x9CD
#define MADERA_DSP3RMIX_INPUT_4_SOURCE			0x9CE
#define MADERA_DSP3RMIX_INPUT_4_VOLUME			0x9CF
#define MADERA_DSP3AUX1MIX_INPUT_1_SOURCE		0x9D0
#define MADERA_DSP3AUX2MIX_INPUT_1_SOURCE		0x9D8
#define MADERA_DSP3AUX3MIX_INPUT_1_SOURCE		0x9E0
#define MADERA_DSP3AUX4MIX_INPUT_1_SOURCE		0x9E8
#define MADERA_DSP3AUX5MIX_INPUT_1_SOURCE		0x9F0
#define MADERA_DSP3AUX6MIX_INPUT_1_SOURCE		0x9F8
#define MADERA_DSP4LMIX_INPUT_1_SOURCE			0xA00
#define MADERA_DSP4LMIX_INPUT_1_VOLUME			0xA01
#define MADERA_DSP4LMIX_INPUT_2_SOURCE			0xA02
#define MADERA_DSP4LMIX_INPUT_2_VOLUME			0xA03
#define MADERA_DSP4LMIX_INPUT_3_SOURCE			0xA04
#define MADERA_DSP4LMIX_INPUT_3_VOLUME			0xA05
#define MADERA_DSP4LMIX_INPUT_4_SOURCE			0xA06
#define MADERA_DSP4LMIX_INPUT_4_VOLUME			0xA07
#define MADERA_DSP4RMIX_INPUT_1_SOURCE			0xA08
#define MADERA_DSP4RMIX_INPUT_1_VOLUME			0xA09
#define MADERA_DSP4RMIX_INPUT_2_SOURCE			0xA0A
#define MADERA_DSP4RMIX_INPUT_2_VOLUME			0xA0B
#define MADERA_DSP4RMIX_INPUT_3_SOURCE			0xA0C
#define MADERA_DSP4RMIX_INPUT_3_VOLUME			0xA0D
#define MADERA_DSP4RMIX_INPUT_4_SOURCE			0xA0E
#define MADERA_DSP4RMIX_INPUT_4_VOLUME			0xA0F
#define MADERA_DSP4AUX1MIX_INPUT_1_SOURCE		0xA10
#define MADERA_DSP4AUX2MIX_INPUT_1_SOURCE		0xA18
#define MADERA_DSP4AUX3MIX_INPUT_1_SOURCE		0xA20
#define MADERA_DSP4AUX4MIX_INPUT_1_SOURCE		0xA28
#define MADERA_DSP4AUX5MIX_INPUT_1_SOURCE		0xA30
#define MADERA_DSP4AUX6MIX_INPUT_1_SOURCE		0xA38
#define MADERA_DSP5LMIX_INPUT_1_SOURCE			0xA40
#define MADERA_DSP5LMIX_INPUT_1_VOLUME			0xA41
#define MADERA_DSP5LMIX_INPUT_2_SOURCE			0xA42
#define MADERA_DSP5LMIX_INPUT_2_VOLUME			0xA43
#define MADERA_DSP5LMIX_INPUT_3_SOURCE			0xA44
#define MADERA_DSP5LMIX_INPUT_3_VOLUME			0xA45
#define MADERA_DSP5LMIX_INPUT_4_SOURCE			0xA46
#define MADERA_DSP5LMIX_INPUT_4_VOLUME			0xA47
#define MADERA_DSP5RMIX_INPUT_1_SOURCE			0xA48
#define MADERA_DSP5RMIX_INPUT_1_VOLUME			0xA49
#define MADERA_DSP5RMIX_INPUT_2_SOURCE			0xA4A
#define MADERA_DSP5RMIX_INPUT_2_VOLUME			0xA4B
#define MADERA_DSP5RMIX_INPUT_3_SOURCE			0xA4C
#define MADERA_DSP5RMIX_INPUT_3_VOLUME			0xA4D
#define MADERA_DSP5RMIX_INPUT_4_SOURCE			0xA4E
#define MADERA_DSP5RMIX_INPUT_4_VOLUME			0xA4F
#define MADERA_DSP5AUX1MIX_INPUT_1_SOURCE		0xA50
#define MADERA_DSP5AUX2MIX_INPUT_1_SOURCE		0xA58
#define MADERA_DSP5AUX3MIX_INPUT_1_SOURCE		0xA60
#define MADERA_DSP5AUX4MIX_INPUT_1_SOURCE		0xA68
#define MADERA_DSP5AUX5MIX_INPUT_1_SOURCE		0xA70
#define MADERA_DSP5AUX6MIX_INPUT_1_SOURCE		0xA78
#define MADERA_ASRC1_1LMIX_INPUT_1_SOURCE		0xA80
#define MADERA_ASRC1_1RMIX_INPUT_1_SOURCE		0xA88
#define MADERA_ASRC1_2LMIX_INPUT_1_SOURCE		0xA90
#define MADERA_ASRC1_2RMIX_INPUT_1_SOURCE		0xA98
#define MADERA_ASRC2_1LMIX_INPUT_1_SOURCE		0xAA0
#define MADERA_ASRC2_1RMIX_INPUT_1_SOURCE		0xAA8
#define MADERA_ASRC2_2LMIX_INPUT_1_SOURCE		0xAB0
#define MADERA_ASRC2_2RMIX_INPUT_1_SOURCE		0xAB8
#define MADERA_ISRC1DEC1MIX_INPUT_1_SOURCE		0xB00
#define MADERA_ISRC1DEC2MIX_INPUT_1_SOURCE		0xB08
#define MADERA_ISRC1DEC3MIX_INPUT_1_SOURCE		0xB10
#define MADERA_ISRC1DEC4MIX_INPUT_1_SOURCE		0xB18
#define MADERA_ISRC1INT1MIX_INPUT_1_SOURCE		0xB20
#define MADERA_ISRC1INT2MIX_INPUT_1_SOURCE		0xB28
#define MADERA_ISRC1INT3MIX_INPUT_1_SOURCE		0xB30
#define MADERA_ISRC1INT4MIX_INPUT_1_SOURCE		0xB38
#define MADERA_ISRC2DEC1MIX_INPUT_1_SOURCE		0xB40
#define MADERA_ISRC2DEC2MIX_INPUT_1_SOURCE		0xB48
#define MADERA_ISRC2DEC3MIX_INPUT_1_SOURCE		0xB50
#define MADERA_ISRC2DEC4MIX_INPUT_1_SOURCE		0xB58
#define MADERA_ISRC2INT1MIX_INPUT_1_SOURCE		0xB60
#define MADERA_ISRC2INT2MIX_INPUT_1_SOURCE		0xB68
#define MADERA_ISRC2INT3MIX_INPUT_1_SOURCE		0xB70
#define MADERA_ISRC2INT4MIX_INPUT_1_SOURCE		0xB78
#define MADERA_ISRC3DEC1MIX_INPUT_1_SOURCE		0xB80
#define MADERA_ISRC3DEC2MIX_INPUT_1_SOURCE		0xB88
#define MADERA_ISRC3DEC3MIX_INPUT_1_SOURCE		0xB90
#define MADERA_ISRC3DEC4MIX_INPUT_1_SOURCE		0xB98
#define MADERA_ISRC3INT1MIX_INPUT_1_SOURCE		0xBA0
#define MADERA_ISRC3INT2MIX_INPUT_1_SOURCE		0xBA8
#define MADERA_ISRC3INT3MIX_INPUT_1_SOURCE		0xBB0
#define MADERA_ISRC3INT4MIX_INPUT_1_SOURCE		0xBB8
#define MADERA_ISRC4DEC1MIX_INPUT_1_SOURCE		0xBC0
#define MADERA_ISRC4DEC2MIX_INPUT_1_SOURCE		0xBC8
#define MADERA_ISRC4INT1MIX_INPUT_1_SOURCE		0xBE0
#define MADERA_ISRC4INT2MIX_INPUT_1_SOURCE		0xBE8
#define MADERA_DSP6LMIX_INPUT_1_SOURCE			0xC00
#define MADERA_DSP6LMIX_INPUT_1_VOLUME			0xC01
#define MADERA_DSP6LMIX_INPUT_2_SOURCE			0xC02
#define MADERA_DSP6LMIX_INPUT_2_VOLUME			0xC03
#define MADERA_DSP6LMIX_INPUT_3_SOURCE			0xC04
#define MADERA_DSP6LMIX_INPUT_3_VOLUME			0xC05
#define MADERA_DSP6LMIX_INPUT_4_SOURCE			0xC06
#define MADERA_DSP6LMIX_INPUT_4_VOLUME			0xC07
#define MADERA_DSP6RMIX_INPUT_1_SOURCE			0xC08
#define MADERA_DSP6RMIX_INPUT_1_VOLUME			0xC09
#define MADERA_DSP6RMIX_INPUT_2_SOURCE			0xC0A
#define MADERA_DSP6RMIX_INPUT_2_VOLUME			0xC0B
#define MADERA_DSP6RMIX_INPUT_3_SOURCE			0xC0C
#define MADERA_DSP6RMIX_INPUT_3_VOLUME			0xC0D
#define MADERA_DSP6RMIX_INPUT_4_SOURCE			0xC0E
#define MADERA_DSP6RMIX_INPUT_4_VOLUME			0xC0F
#define MADERA_DSP6AUX1MIX_INPUT_1_SOURCE		0xC10
#define MADERA_DSP6AUX2MIX_INPUT_1_SOURCE		0xC18
#define MADERA_DSP6AUX3MIX_INPUT_1_SOURCE		0xC20
#define MADERA_DSP6AUX4MIX_INPUT_1_SOURCE		0xC28
#define MADERA_DSP6AUX5MIX_INPUT_1_SOURCE		0xC30
#define MADERA_DSP6AUX6MIX_INPUT_1_SOURCE		0xC38
#define MADERA_DSP7LMIX_INPUT_1_SOURCE			0xC40
#define MADERA_DSP7LMIX_INPUT_1_VOLUME			0xC41
#define MADERA_DSP7LMIX_INPUT_2_SOURCE			0xC42
#define MADERA_DSP7LMIX_INPUT_2_VOLUME			0xC43
#define MADERA_DSP7LMIX_INPUT_3_SOURCE			0xC44
#define MADERA_DSP7LMIX_INPUT_3_VOLUME			0xC45
#define MADERA_DSP7LMIX_INPUT_4_SOURCE			0xC46
#define MADERA_DSP7LMIX_INPUT_4_VOLUME			0xC47
#define MADERA_DSP7RMIX_INPUT_1_SOURCE			0xC48
#define MADERA_DSP7RMIX_INPUT_1_VOLUME			0xC49
#define MADERA_DSP7RMIX_INPUT_2_SOURCE			0xC4A
#define MADERA_DSP7RMIX_INPUT_2_VOLUME			0xC4B
#define MADERA_DSP7RMIX_INPUT_3_SOURCE			0xC4C
#define MADERA_DSP7RMIX_INPUT_3_VOLUME			0xC4D
#define MADERA_DSP7RMIX_INPUT_4_SOURCE			0xC4E
#define MADERA_DSP7RMIX_INPUT_4_VOLUME			0xC4F
#define MADERA_DSP7AUX1MIX_INPUT_1_SOURCE		0xC50
#define MADERA_DSP7AUX2MIX_INPUT_1_SOURCE		0xC58
#define MADERA_DSP7AUX3MIX_INPUT_1_SOURCE		0xC60
#define MADERA_DSP7AUX4MIX_INPUT_1_SOURCE		0xC68
#define MADERA_DSP7AUX5MIX_INPUT_1_SOURCE		0xC70
#define MADERA_DSP7AUX6MIX_INPUT_1_SOURCE		0xC78
#define MADERA_DFC1MIX_INPUT_1_SOURCE			0xDC0
#define MADERA_DFC2MIX_INPUT_1_SOURCE			0xDC8
#define MADERA_DFC3MIX_INPUT_1_SOURCE			0xDD0
#define MADERA_DFC4MIX_INPUT_1_SOURCE			0xDD8
#define MADERA_DFC5MIX_INPUT_1_SOURCE			0xDE0
#define MADERA_DFC6MIX_INPUT_1_SOURCE			0xDE8
#define MADERA_DFC7MIX_INPUT_1_SOURCE			0xDF0
#define MADERA_DFC8MIX_INPUT_1_SOURCE			0xDF8
#define MADERA_FX_CTRL1					0xE00
#define MADERA_FX_CTRL2					0xE01
#define MADERA_EQ1_1					0xE10
#define MADERA_EQ1_2					0xE11
#define MADERA_EQ1_21					0xE24
#define MADERA_EQ2_1					0xE26
#define MADERA_EQ2_2					0xE27
#define MADERA_EQ2_21					0xE3A
#define MADERA_EQ3_1					0xE3C
#define MADERA_EQ3_2					0xE3D
#define MADERA_EQ3_21					0xE50
#define MADERA_EQ4_1					0xE52
#define MADERA_EQ4_2					0xE53
#define MADERA_EQ4_21					0xE66
#define MADERA_DRC1_CTRL1				0xE80
#define MADERA_DRC1_CTRL2				0xE81
#define MADERA_DRC1_CTRL3				0xE82
#define MADERA_DRC1_CTRL4				0xE83
#define MADERA_DRC1_CTRL5				0xE84
#define MADERA_DRC2_CTRL1				0xE88
#define MADERA_DRC2_CTRL2				0xE89
#define MADERA_DRC2_CTRL3				0xE8A
#define MADERA_DRC2_CTRL4				0xE8B
#define MADERA_DRC2_CTRL5				0xE8C
#define MADERA_HPLPF1_1					0xEC0
#define MADERA_HPLPF1_2					0xEC1
#define MADERA_HPLPF2_1					0xEC4
#define MADERA_HPLPF2_2					0xEC5
#define MADERA_HPLPF3_1					0xEC8
#define MADERA_HPLPF3_2					0xEC9
#define MADERA_HPLPF4_1					0xECC
#define MADERA_HPLPF4_2					0xECD
#define MADERA_ASRC2_ENABLE				0xED0
#define MADERA_ASRC2_STATUS				0xED1
#define MADERA_ASRC2_RATE1				0xED2
#define MADERA_ASRC2_RATE2				0xED3
#define MADERA_ASRC1_ENABLE				0xEE0
#define MADERA_ASRC1_STATUS				0xEE1
#define MADERA_ASRC1_RATE1				0xEE2
#define MADERA_ASRC1_RATE2				0xEE3
#define MADERA_ISRC_1_CTRL_1				0xEF0
#define MADERA_ISRC_1_CTRL_2				0xEF1
#define MADERA_ISRC_1_CTRL_3				0xEF2
#define MADERA_ISRC_2_CTRL_1				0xEF3
#define MADERA_ISRC_2_CTRL_2				0xEF4
#define MADERA_ISRC_2_CTRL_3				0xEF5
#define MADERA_ISRC_3_CTRL_1				0xEF6
#define MADERA_ISRC_3_CTRL_2				0xEF7
#define MADERA_ISRC_3_CTRL_3				0xEF8
#define MADERA_ISRC_4_CTRL_1				0xEF9
#define MADERA_ISRC_4_CTRL_2				0xEFA
#define MADERA_ISRC_4_CTRL_3				0xEFB
#define MADERA_CLOCK_CONTROL				0xF00
#define MADERA_ANC_SRC					0xF01
#define MADERA_DSP_STATUS				0xF02
#define MADERA_ANC_COEFF_START				0xF08
#define MADERA_ANC_COEFF_END				0xF12
#define MADERA_FCL_FILTER_CONTROL			0xF15
#define MADERA_FCL_ADC_REFORMATTER_CONTROL		0xF17
#define MADERA_FCL_COEFF_START				0xF18
#define MADERA_FCL_COEFF_END				0xF69
#define MADERA_FCR_FILTER_CONTROL			0xF71
#define MADERA_FCR_ADC_REFORMATTER_CONTROL		0xF73
#define MADERA_FCR_COEFF_START				0xF74
#define MADERA_FCR_COEFF_END				0xFC5
#define MADERA_AUXPDM1_CTRL_0				0x10C0
#define MADERA_AUXPDM1_CTRL_1				0x10C1
#define MADERA_DFC1_CTRL				0x1480
#define MADERA_DFC1_RX					0x1482
#define MADERA_DFC1_TX					0x1484
#define MADERA_DFC2_CTRL				0x1486
#define MADERA_DFC2_RX					0x1488
#define MADERA_DFC2_TX					0x148A
#define MADERA_DFC3_CTRL				0x148C
#define MADERA_DFC3_RX					0x148E
#define MADERA_DFC3_TX					0x1490
#define MADERA_DFC4_CTRL				0x1492
#define MADERA_DFC4_RX					0x1494
#define MADERA_DFC4_TX					0x1496
#define MADERA_DFC5_CTRL				0x1498
#define MADERA_DFC5_RX					0x149A
#define MADERA_DFC5_TX					0x149C
#define MADERA_DFC6_CTRL				0x149E
#define MADERA_DFC6_RX					0x14A0
#define MADERA_DFC6_TX					0x14A2
#define MADERA_DFC7_CTRL				0x14A4
#define MADERA_DFC7_RX					0x14A6
#define MADERA_DFC7_TX					0x14A8
#define MADERA_DFC8_CTRL				0x14AA
#define MADERA_DFC8_RX					0x14AC
#define MADERA_DFC8_TX					0x14AE
#define MADERA_DFC_STATUS				0x14B6
#define MADERA_ADSP2_IRQ0				0x1600
#define MADERA_ADSP2_IRQ1				0x1601
#define MADERA_ADSP2_IRQ2				0x1602
#define MADERA_ADSP2_IRQ3				0x1603
#define MADERA_ADSP2_IRQ4				0x1604
#define MADERA_ADSP2_IRQ5				0x1605
#define MADERA_ADSP2_IRQ6				0x1606
#define MADERA_ADSP2_IRQ7				0x1607
#define MADERA_GPIO1_CTRL_1				0x1700
#define MADERA_GPIO1_CTRL_2				0x1701
#define MADERA_GPIO2_CTRL_1				0x1702
#define MADERA_GPIO2_CTRL_2				0x1703
#define MADERA_GPIO15_CTRL_1				0x171C
#define MADERA_GPIO15_CTRL_2				0x171D
#define MADERA_GPIO16_CTRL_1				0x171E
#define MADERA_GPIO16_CTRL_2				0x171F
#define MADERA_GPIO38_CTRL_1				0x174A
#define MADERA_GPIO38_CTRL_2				0x174B
#define MADERA_GPIO40_CTRL_1				0x174E
#define MADERA_GPIO40_CTRL_2				0x174F
#define MADERA_IRQ1_STATUS_1				0x1800
#define MADERA_IRQ1_STATUS_2				0x1801
#define MADERA_IRQ1_STATUS_6				0x1805
#define MADERA_IRQ1_STATUS_7				0x1806
#define MADERA_IRQ1_STATUS_9				0x1808
#define MADERA_IRQ1_STATUS_11				0x180A
#define MADERA_IRQ1_STATUS_12				0x180B
#define MADERA_IRQ1_STATUS_15				0x180E
#define MADERA_IRQ1_STATUS_33				0x1820
#define MADERA_IRQ1_MASK_1				0x1840
#define MADERA_IRQ1_MASK_2				0x1841
#define MADERA_IRQ1_MASK_6				0x1845
#define MADERA_IRQ1_MASK_33				0x1860
#define MADERA_IRQ1_RAW_STATUS_1			0x1880
#define MADERA_IRQ1_RAW_STATUS_2			0x1881
#define MADERA_IRQ1_RAW_STATUS_7			0x1886
#define MADERA_IRQ1_RAW_STATUS_15			0x188E
#define MADERA_IRQ1_RAW_STATUS_33			0x18A0
#define MADERA_INTERRUPT_DEBOUNCE_7			0x1A06
#define MADERA_INTERRUPT_DEBOUNCE_15			0x1A0E
#define MADERA_IRQ1_CTRL				0x1A80
#define MADERA_IRQ2_CTRL				0x1A82
#define MADERA_INTERRUPT_RAW_STATUS_1			0x1AA0
#define MADERA_WSEQ_SEQUENCE_1				0x3000
#define MADERA_WSEQ_SEQUENCE_225			0x31C0
#define MADERA_WSEQ_SEQUENCE_252			0x31F6
#define CS47L35_OTP_HPDET_CAL_1				0x31F8
#define CS47L35_OTP_HPDET_CAL_2				0x31FA
#define MADERA_WSEQ_SEQUENCE_508			0x33F6
#define CS47L85_OTP_HPDET_CAL_1				0x33F8
#define CS47L85_OTP_HPDET_CAL_2				0x33FA
#define MADERA_OTP_HPDET_CAL_1				0x20004
#define MADERA_OTP_HPDET_CAL_2				0x20006
#define MADERA_DSP1_CONFIG_1				0x0FFE00
#define MADERA_DSP1_CONFIG_2				0x0FFE02
#define MADERA_DSP1_SCRATCH_1				0x0FFE40
#define MADERA_DSP1_SCRATCH_2				0x0FFE42
#define MADERA_DSP1_PMEM_ERR_ADDR___XMEM_ERR_ADDR	0xFFE7C
#define MADERA_DSP2_CONFIG_1				0x17FE00
#define MADERA_DSP2_CONFIG_2				0x17FE02
#define MADERA_DSP2_SCRATCH_1				0x17FE40
#define MADERA_DSP2_SCRATCH_2				0x17FE42
#define MADERA_DSP2_PMEM_ERR_ADDR___XMEM_ERR_ADDR	0x17FE7C
#define MADERA_DSP3_CONFIG_1				0x1FFE00
#define MADERA_DSP3_CONFIG_2				0x1FFE02
#define MADERA_DSP3_SCRATCH_1				0x1FFE40
#define MADERA_DSP3_SCRATCH_2				0x1FFE42
#define MADERA_DSP3_PMEM_ERR_ADDR___XMEM_ERR_ADDR	0x1FFE7C
#define MADERA_DSP4_CONFIG_1				0x27FE00
#define MADERA_DSP4_CONFIG_2				0x27FE02
#define MADERA_DSP4_SCRATCH_1				0x27FE40
#define MADERA_DSP4_SCRATCH_2				0x27FE42
#define MADERA_DSP4_PMEM_ERR_ADDR___XMEM_ERR_ADDR	0x27FE7C
#define MADERA_DSP5_CONFIG_1				0x2FFE00
#define MADERA_DSP5_CONFIG_2				0x2FFE02
#define MADERA_DSP5_SCRATCH_1				0x2FFE40
#define MADERA_DSP5_SCRATCH_2				0x2FFE42
#define MADERA_DSP5_PMEM_ERR_ADDR___XMEM_ERR_ADDR	0x2FFE7C
#define MADERA_DSP6_CONFIG_1				0x37FE00
#define MADERA_DSP6_CONFIG_2				0x37FE02
#define MADERA_DSP6_SCRATCH_1				0x37FE40
#define MADERA_DSP6_SCRATCH_2				0x37FE42
#define MADERA_DSP6_PMEM_ERR_ADDR___XMEM_ERR_ADDR	0x37FE7C
#define MADERA_DSP7_CONFIG_1				0x3FFE00
#define MADERA_DSP7_CONFIG_2				0x3FFE02
#define MADERA_DSP7_SCRATCH_1				0x3FFE40
#define MADERA_DSP7_SCRATCH_2				0x3FFE42
#define MADERA_DSP7_PMEM_ERR_ADDR___XMEM_ERR_ADDR	0x3FFE7C

/* (0x0000)  Software_Reset */
#define MADERA_SW_RST_DEV_ID1_MASK			0xFFFF
#define MADERA_SW_RST_DEV_ID1_SHIFT			     0
#define MADERA_SW_RST_DEV_ID1_WIDTH			    16

/* (0x0001)  Hardware_Revision */
#define MADERA_HW_REVISION_MASK				0x00FF
#define MADERA_HW_REVISION_SHIFT			     0
#define MADERA_HW_REVISION_WIDTH			     8

/* (0x0020)  Tone_Generator_1 */
#define MADERA_TONE2_ENA				0x0002
#define MADERA_TONE2_ENA_MASK				0x0002
#define MADERA_TONE2_ENA_SHIFT				     1
#define MADERA_TONE2_ENA_WIDTH				     1
#define MADERA_TONE1_ENA				0x0001
#define MADERA_TONE1_ENA_MASK				0x0001
#define MADERA_TONE1_ENA_SHIFT				     0
#define MADERA_TONE1_ENA_WIDTH				     1

/* (0x0021)  Tone_Generator_2 */
#define MADERA_TONE1_LVL_0_MASK				0xFFFF
#define MADERA_TONE1_LVL_0_SHIFT			     0
#define MADERA_TONE1_LVL_0_WIDTH			    16

/* (0x0022)  Tone_Generator_3 */
#define MADERA_TONE1_LVL_MASK				0x00FF
#define MADERA_TONE1_LVL_SHIFT				     0
#define MADERA_TONE1_LVL_WIDTH				     8

/* (0x0023)  Tone_Generator_4 */
#define MADERA_TONE2_LVL_0_MASK				0xFFFF
#define MADERA_TONE2_LVL_0_SHIFT			     0
#define MADERA_TONE2_LVL_0_WIDTH			    16

/* (0x0024)  Tone_Generator_5 */
#define MADERA_TONE2_LVL_MASK				0x00FF
#define MADERA_TONE2_LVL_SHIFT				     0
#define MADERA_TONE2_LVL_WIDTH				     8

/* (0x0030)  PWM_Drive_1 */
#define MADERA_PWM2_ENA					0x0002
#define MADERA_PWM2_ENA_MASK				0x0002
#define MADERA_PWM2_ENA_SHIFT				     1
#define MADERA_PWM2_ENA_WIDTH				     1
#define MADERA_PWM1_ENA					0x0001
#define MADERA_PWM1_ENA_MASK				0x0001
#define MADERA_PWM1_ENA_SHIFT				     0
#define MADERA_PWM1_ENA_WIDTH				     1

/* (0x00A0)  Comfort_Noise_Generator */
#define MADERA_NOISE_GEN_ENA				0x0020
#define MADERA_NOISE_GEN_ENA_MASK			0x0020
#define MADERA_NOISE_GEN_ENA_SHIFT			     5
#define MADERA_NOISE_GEN_ENA_WIDTH			     1
#define MADERA_NOISE_GEN_GAIN_MASK			0x001F
#define MADERA_NOISE_GEN_GAIN_SHIFT			     0
#define MADERA_NOISE_GEN_GAIN_WIDTH			     5

/* (0x0100)  Clock_32k_1 */
#define MADERA_CLK_32K_ENA				0x0040
#define MADERA_CLK_32K_ENA_MASK				0x0040
#define MADERA_CLK_32K_ENA_SHIFT			     6
#define MADERA_CLK_32K_ENA_WIDTH			     1
#define MADERA_CLK_32K_SRC_MASK				0x0003
#define MADERA_CLK_32K_SRC_SHIFT			     0
#define MADERA_CLK_32K_SRC_WIDTH			     2

/* (0x0101)  System_Clock_1 */
#define MADERA_SYSCLK_FRAC				0x8000
#define MADERA_SYSCLK_FRAC_MASK				0x8000
#define MADERA_SYSCLK_FRAC_SHIFT			    15
#define MADERA_SYSCLK_FRAC_WIDTH			     1
#define MADERA_SYSCLK_FREQ_MASK				0x0700
#define MADERA_SYSCLK_FREQ_SHIFT			     8
#define MADERA_SYSCLK_FREQ_WIDTH			     3
#define MADERA_SYSCLK_ENA				0x0040
#define MADERA_SYSCLK_ENA_MASK				0x0040
#define MADERA_SYSCLK_ENA_SHIFT				     6
#define MADERA_SYSCLK_ENA_WIDTH				     1
#define MADERA_SYSCLK_SRC_MASK				0x000F
#define MADERA_SYSCLK_SRC_SHIFT				     0
#define MADERA_SYSCLK_SRC_WIDTH				     4

/* (0x0102)  Sample_rate_1 */
#define MADERA_SAMPLE_RATE_1_MASK			0x001F
#define MADERA_SAMPLE_RATE_1_SHIFT			     0
#define MADERA_SAMPLE_RATE_1_WIDTH			     5

/* (0x0103)  Sample_rate_2 */
#define MADERA_SAMPLE_RATE_2_MASK			0x001F
#define MADERA_SAMPLE_RATE_2_SHIFT			     0
#define MADERA_SAMPLE_RATE_2_WIDTH			     5

/* (0x0104)  Sample_rate_3 */
#define MADERA_SAMPLE_RATE_3_MASK			0x001F
#define MADERA_SAMPLE_RATE_3_SHIFT			     0
#define MADERA_SAMPLE_RATE_3_WIDTH			     5

/* (0x0112)  Async_clock_1 */
#define MADERA_ASYNC_CLK_FREQ_MASK			0x0700
#define MADERA_ASYNC_CLK_FREQ_SHIFT			     8
#define MADERA_ASYNC_CLK_FREQ_WIDTH			     3
#define MADERA_ASYNC_CLK_ENA				0x0040
#define MADERA_ASYNC_CLK_ENA_MASK			0x0040
#define MADERA_ASYNC_CLK_ENA_SHIFT			     6
#define MADERA_ASYNC_CLK_ENA_WIDTH			     1
#define MADERA_ASYNC_CLK_SRC_MASK			0x000F
#define MADERA_ASYNC_CLK_SRC_SHIFT			     0
#define MADERA_ASYNC_CLK_SRC_WIDTH			     4

/* (0x0113)  Async_sample_rate_1 */
#define MADERA_ASYNC_SAMPLE_RATE_1_MASK			0x001F
#define MADERA_ASYNC_SAMPLE_RATE_1_SHIFT		     0
#define MADERA_ASYNC_SAMPLE_RATE_1_WIDTH		     5

/* (0x0114)  Async_sample_rate_2 */
#define MADERA_ASYNC_SAMPLE_RATE_2_MASK			0x001F
#define MADERA_ASYNC_SAMPLE_RATE_2_SHIFT		     0
#define MADERA_ASYNC_SAMPLE_RATE_2_WIDTH		     5

/* (0x0120)  DSP_Clock_1 */
#define MADERA_DSP_CLK_FREQ_LEGACY			0x0700
#define MADERA_DSP_CLK_FREQ_LEGACY_MASK			0x0700
#define MADERA_DSP_CLK_FREQ_LEGACY_SHIFT		     8
#define MADERA_DSP_CLK_FREQ_LEGACY_WIDTH		     3
#define MADERA_DSP_CLK_ENA				0x0040
#define MADERA_DSP_CLK_ENA_MASK				0x0040
#define MADERA_DSP_CLK_ENA_SHIFT			     6
#define MADERA_DSP_CLK_ENA_WIDTH			     1
#define MADERA_DSP_CLK_SRC				0x000F
#define MADERA_DSP_CLK_SRC_MASK				0x000F
#define MADERA_DSP_CLK_SRC_SHIFT			     0
#define MADERA_DSP_CLK_SRC_WIDTH			     4

/* (0x0122)  DSP_Clock_2 */
#define MADERA_DSP_CLK_FREQ_MASK			0x03FF
#define MADERA_DSP_CLK_FREQ_SHIFT			     0
#define MADERA_DSP_CLK_FREQ_WIDTH			    10

/* (0x0149)  Output_system_clock */
#define MADERA_OPCLK_ENA				0x8000
#define MADERA_OPCLK_ENA_MASK				0x8000
#define MADERA_OPCLK_ENA_SHIFT				    15
#define MADERA_OPCLK_ENA_WIDTH				     1
#define MADERA_OPCLK_DIV_MASK				0x00F8
#define MADERA_OPCLK_DIV_SHIFT				     3
#define MADERA_OPCLK_DIV_WIDTH				     5
#define MADERA_OPCLK_SEL_MASK				0x0007
#define MADERA_OPCLK_SEL_SHIFT				     0
#define MADERA_OPCLK_SEL_WIDTH				     3

/* (0x014A)  Output_async_clock */
#define MADERA_OPCLK_ASYNC_ENA				0x8000
#define MADERA_OPCLK_ASYNC_ENA_MASK			0x8000
#define MADERA_OPCLK_ASYNC_ENA_SHIFT			    15
#define MADERA_OPCLK_ASYNC_ENA_WIDTH			     1
#define MADERA_OPCLK_ASYNC_DIV_MASK			0x00F8
#define MADERA_OPCLK_ASYNC_DIV_SHIFT			     3
#define MADERA_OPCLK_ASYNC_DIV_WIDTH			     5
#define MADERA_OPCLK_ASYNC_SEL_MASK			0x0007
#define MADERA_OPCLK_ASYNC_SEL_SHIFT			     0
#define MADERA_OPCLK_ASYNC_SEL_WIDTH			     3

/* (0x0171)  FLL1_Control_1 */
#define CS47L92_FLL1_REFCLK_SRC_MASK			0xF000
#define CS47L92_FLL1_REFCLK_SRC_SHIFT			    12
#define CS47L92_FLL1_REFCLK_SRC_WIDTH			     4
#define MADERA_FLL1_HOLD_MASK				0x0004
#define MADERA_FLL1_HOLD_SHIFT				     2
#define MADERA_FLL1_HOLD_WIDTH				     1
#define MADERA_FLL1_FREERUN				0x0002
#define MADERA_FLL1_FREERUN_MASK			0x0002
#define MADERA_FLL1_FREERUN_SHIFT			     1
#define MADERA_FLL1_FREERUN_WIDTH			     1
#define MADERA_FLL1_ENA					0x0001
#define MADERA_FLL1_ENA_MASK				0x0001
#define MADERA_FLL1_ENA_SHIFT				     0
#define MADERA_FLL1_ENA_WIDTH				     1

/* (0x0172)  FLL1_Control_2 */
#define MADERA_FLL1_CTRL_UPD				0x8000
#define MADERA_FLL1_CTRL_UPD_MASK			0x8000
#define MADERA_FLL1_CTRL_UPD_SHIFT			    15
#define MADERA_FLL1_CTRL_UPD_WIDTH			     1
#define MADERA_FLL1_N_MASK				0x03FF
#define MADERA_FLL1_N_SHIFT				     0
#define MADERA_FLL1_N_WIDTH				    10

/* (0x0173)  FLL1_Control_3 */
#define MADERA_FLL1_THETA_MASK				0xFFFF
#define MADERA_FLL1_THETA_SHIFT				     0
#define MADERA_FLL1_THETA_WIDTH				    16

/* (0x0174)  FLL1_Control_4 */
#define MADERA_FLL1_LAMBDA_MASK				0xFFFF
#define MADERA_FLL1_LAMBDA_SHIFT			     0
#define MADERA_FLL1_LAMBDA_WIDTH			    16

/* (0x0175)  FLL1_Control_5 */
#define MADERA_FLL1_FRATIO_MASK				0x0F00
#define MADERA_FLL1_FRATIO_SHIFT			     8
#define MADERA_FLL1_FRATIO_WIDTH			     4
#define MADERA_FLL1_FB_DIV_MASK				0x03FF
#define MADERA_FLL1_FB_DIV_SHIFT			     0
#define MADERA_FLL1_FB_DIV_WIDTH			    10

/* (0x0176)  FLL1_Control_6 */
#define MADERA_FLL1_REFCLK_DIV_MASK			0x00C0
#define MADERA_FLL1_REFCLK_DIV_SHIFT			     6
#define MADERA_FLL1_REFCLK_DIV_WIDTH			     2
#define MADERA_FLL1_REFCLK_SRC_MASK			0x000F
#define MADERA_FLL1_REFCLK_SRC_SHIFT			     0
#define MADERA_FLL1_REFCLK_SRC_WIDTH			     4

/* (0x0179)  FLL1_Control_7 */
#define MADERA_FLL1_GAIN_MASK				0x003c
#define MADERA_FLL1_GAIN_SHIFT				     2
#define MADERA_FLL1_GAIN_WIDTH				     4

/* (0x017A)  FLL1_EFS_2 */
#define MADERA_FLL1_PHASE_GAIN_MASK			0xF000
#define MADERA_FLL1_PHASE_GAIN_SHIFT			    12
#define MADERA_FLL1_PHASE_GAIN_WIDTH			     4
#define MADERA_FLL1_PHASE_ENA_MASK			0x0800
#define MADERA_FLL1_PHASE_ENA_SHIFT			    11
#define MADERA_FLL1_PHASE_ENA_WIDTH			     1

/* (0x017A)  FLL1_Control_10 */
#define MADERA_FLL1_HP_MASK				0xC000
#define MADERA_FLL1_HP_SHIFT				    14
#define MADERA_FLL1_HP_WIDTH				     2
#define MADERA_FLL1_PHASEDET_ENA_MASK			0x1000
#define MADERA_FLL1_PHASEDET_ENA_SHIFT			    12
#define MADERA_FLL1_PHASEDET_ENA_WIDTH			     1

/* (0x017B)  FLL1_Control_11 */
#define MADERA_FLL1_LOCKDET_THR_MASK			0x001E
#define MADERA_FLL1_LOCKDET_THR_SHIFT			     1
#define MADERA_FLL1_LOCKDET_THR_WIDTH			     4
#define MADERA_FLL1_LOCKDET_MASK			0x0001
#define MADERA_FLL1_LOCKDET_SHIFT			     0
#define MADERA_FLL1_LOCKDET_WIDTH			     1

/* (0x017D)  FLL1_Digital_Test_1 */
#define MADERA_FLL1_SYNC_EFS_ENA_MASK			0x0100
#define MADERA_FLL1_SYNC_EFS_ENA_SHIFT			     8
#define MADERA_FLL1_SYNC_EFS_ENA_WIDTH			     1
#define MADERA_FLL1_CLK_VCO_FAST_SRC_MASK		0x0003
#define MADERA_FLL1_CLK_VCO_FAST_SRC_SHIFT		     0
#define MADERA_FLL1_CLK_VCO_FAST_SRC_WIDTH		     2

/* (0x0181)  FLL1_Synchroniser_1 */
#define MADERA_FLL1_SYNC_ENA				0x0001
#define MADERA_FLL1_SYNC_ENA_MASK			0x0001
#define MADERA_FLL1_SYNC_ENA_SHIFT			     0
#define MADERA_FLL1_SYNC_ENA_WIDTH			     1

/* (0x0182)  FLL1_Synchroniser_2 */
#define MADERA_FLL1_SYNC_N_MASK				0x03FF
#define MADERA_FLL1_SYNC_N_SHIFT			     0
#define MADERA_FLL1_SYNC_N_WIDTH			    10

/* (0x0183)  FLL1_Synchroniser_3 */
#define MADERA_FLL1_SYNC_THETA_MASK			0xFFFF
#define MADERA_FLL1_SYNC_THETA_SHIFT			     0
#define MADERA_FLL1_SYNC_THETA_WIDTH			    16

/* (0x0184)  FLL1_Synchroniser_4 */
#define MADERA_FLL1_SYNC_LAMBDA_MASK			0xFFFF
#define MADERA_FLL1_SYNC_LAMBDA_SHIFT			     0
#define MADERA_FLL1_SYNC_LAMBDA_WIDTH			    16

/* (0x0185)  FLL1_Synchroniser_5 */
#define MADERA_FLL1_SYNC_FRATIO_MASK			0x0700
#define MADERA_FLL1_SYNC_FRATIO_SHIFT			     8
#define MADERA_FLL1_SYNC_FRATIO_WIDTH			     3

/* (0x0186)  FLL1_Synchroniser_6 */
#define MADERA_FLL1_SYNCCLK_DIV_MASK			0x00C0
#define MADERA_FLL1_SYNCCLK_DIV_SHIFT			     6
#define MADERA_FLL1_SYNCCLK_DIV_WIDTH			     2
#define MADERA_FLL1_SYNCCLK_SRC_MASK			0x000F
#define MADERA_FLL1_SYNCCLK_SRC_SHIFT			     0
#define MADERA_FLL1_SYNCCLK_SRC_WIDTH			     4

/* (0x0187)  FLL1_Synchroniser_7 */
#define MADERA_FLL1_SYNC_GAIN_MASK			0x003c
#define MADERA_FLL1_SYNC_GAIN_SHIFT			     2
#define MADERA_FLL1_SYNC_GAIN_WIDTH			     4
#define MADERA_FLL1_SYNC_DFSAT				0x0001
#define MADERA_FLL1_SYNC_DFSAT_MASK			0x0001
#define MADERA_FLL1_SYNC_DFSAT_SHIFT			     0
#define MADERA_FLL1_SYNC_DFSAT_WIDTH			     1

/* (0x01D1)  FLL_AO_Control_1 */
#define MADERA_FLL_AO_HOLD				0x0004
#define MADERA_FLL_AO_HOLD_MASK				0x0004
#define MADERA_FLL_AO_HOLD_SHIFT			     2
#define MADERA_FLL_AO_HOLD_WIDTH			     1
#define MADERA_FLL_AO_FREERUN				0x0002
#define MADERA_FLL_AO_FREERUN_MASK			0x0002
#define MADERA_FLL_AO_FREERUN_SHIFT			     1
#define MADERA_FLL_AO_FREERUN_WIDTH			     1
#define MADERA_FLL_AO_ENA				0x0001
#define MADERA_FLL_AO_ENA_MASK				0x0001
#define MADERA_FLL_AO_ENA_SHIFT				     0
#define MADERA_FLL_AO_ENA_WIDTH				     1

/* (0x01D2)  FLL_AO_Control_2 */
#define MADERA_FLL_AO_CTRL_UPD				0x8000
#define MADERA_FLL_AO_CTRL_UPD_MASK			0x8000
#define MADERA_FLL_AO_CTRL_UPD_SHIFT			    15
#define MADERA_FLL_AO_CTRL_UPD_WIDTH			     1

/* (0x01D6)  FLL_AO_Control_6 */
#define MADERA_FLL_AO_REFCLK_SRC_MASK			0x000F
#define MADERA_FLL_AO_REFCLK_SRC_SHIFT			     0
#define MADERA_FLL_AO_REFCLK_SRC_WIDTH			     4

/* (0x0200)  Mic_Charge_Pump_1 */
#define MADERA_CPMIC_BYPASS				0x0002
#define MADERA_CPMIC_BYPASS_MASK			0x0002
#define MADERA_CPMIC_BYPASS_SHIFT			     1
#define MADERA_CPMIC_BYPASS_WIDTH			     1
#define MADERA_CPMIC_ENA				0x0001
#define MADERA_CPMIC_ENA_MASK				0x0001
#define MADERA_CPMIC_ENA_SHIFT				     0
#define MADERA_CPMIC_ENA_WIDTH				     1

/* (0x0210)  LDO1_Control_1 */
#define MADERA_LDO1_VSEL_MASK				0x07E0
#define MADERA_LDO1_VSEL_SHIFT				     5
#define MADERA_LDO1_VSEL_WIDTH				     6
#define MADERA_LDO1_FAST				0x0010
#define MADERA_LDO1_FAST_MASK				0x0010
#define MADERA_LDO1_FAST_SHIFT				     4
#define MADERA_LDO1_FAST_WIDTH				     1
#define MADERA_LDO1_DISCH				0x0004
#define MADERA_LDO1_DISCH_MASK				0x0004
#define MADERA_LDO1_DISCH_SHIFT				     2
#define MADERA_LDO1_DISCH_WIDTH				     1
#define MADERA_LDO1_BYPASS				0x0002
#define MADERA_LDO1_BYPASS_MASK				0x0002
#define MADERA_LDO1_BYPASS_SHIFT			     1
#define MADERA_LDO1_BYPASS_WIDTH			     1
#define MADERA_LDO1_ENA					0x0001
#define MADERA_LDO1_ENA_MASK				0x0001
#define MADERA_LDO1_ENA_SHIFT				     0
#define MADERA_LDO1_ENA_WIDTH				     1

/* (0x0213)  LDO2_Control_1 */
#define MADERA_LDO2_VSEL_MASK				0x07E0
#define MADERA_LDO2_VSEL_SHIFT				     5
#define MADERA_LDO2_VSEL_WIDTH				     6
#define MADERA_LDO2_FAST				0x0010
#define MADERA_LDO2_FAST_MASK				0x0010
#define MADERA_LDO2_FAST_SHIFT				     4
#define MADERA_LDO2_FAST_WIDTH				     1
#define MADERA_LDO2_DISCH				0x0004
#define MADERA_LDO2_DISCH_MASK				0x0004
#define MADERA_LDO2_DISCH_SHIFT				     2
#define MADERA_LDO2_DISCH_WIDTH				     1
#define MADERA_LDO2_BYPASS				0x0002
#define MADERA_LDO2_BYPASS_MASK				0x0002
#define MADERA_LDO2_BYPASS_SHIFT			     1
#define MADERA_LDO2_BYPASS_WIDTH			     1
#define MADERA_LDO2_ENA					0x0001
#define MADERA_LDO2_ENA_MASK				0x0001
#define MADERA_LDO2_ENA_SHIFT				     0
#define MADERA_LDO2_ENA_WIDTH				     1

/* (0x0218)  Mic_Bias_Ctrl_1 */
#define MADERA_MICB1_EXT_CAP				0x8000
#define MADERA_MICB1_EXT_CAP_MASK			0x8000
#define MADERA_MICB1_EXT_CAP_SHIFT			    15
#define MADERA_MICB1_EXT_CAP_WIDTH			     1
#define MADERA_MICB1_LVL_MASK				0x01E0
#define MADERA_MICB1_LVL_SHIFT				     5
#define MADERA_MICB1_LVL_WIDTH				     4
#define MADERA_MICB1_ENA				0x0001
#define MADERA_MICB1_ENA_MASK				0x0001
#define MADERA_MICB1_ENA_SHIFT				     0
#define MADERA_MICB1_ENA_WIDTH				     1

/* (0x021C)  Mic_Bias_Ctrl_5 */
#define MADERA_MICB1D_ENA				0x1000
#define MADERA_MICB1D_ENA_MASK				0x1000
#define MADERA_MICB1D_ENA_SHIFT				    12
#define MADERA_MICB1D_ENA_WIDTH				     1
#define MADERA_MICB1C_ENA				0x0100
#define MADERA_MICB1C_ENA_MASK				0x0100
#define MADERA_MICB1C_ENA_SHIFT				     8
#define MADERA_MICB1C_ENA_WIDTH				     1
#define MADERA_MICB1B_ENA				0x0010
#define MADERA_MICB1B_ENA_MASK				0x0010
#define MADERA_MICB1B_ENA_SHIFT				     4
#define MADERA_MICB1B_ENA_WIDTH				     1
#define MADERA_MICB1A_ENA				0x0001
#define MADERA_MICB1A_ENA_MASK				0x0001
#define MADERA_MICB1A_ENA_SHIFT				     0
#define MADERA_MICB1A_ENA_WIDTH				     1

/* (0x021E)  Mic_Bias_Ctrl_6 */
#define MADERA_MICB2D_ENA				0x1000
#define MADERA_MICB2D_ENA_MASK				0x1000
#define MADERA_MICB2D_ENA_SHIFT				    12
#define MADERA_MICB2D_ENA_WIDTH				     1
#define MADERA_MICB2C_ENA				0x0100
#define MADERA_MICB2C_ENA_MASK				0x0100
#define MADERA_MICB2C_ENA_SHIFT				     8
#define MADERA_MICB2C_ENA_WIDTH				     1
#define MADERA_MICB2B_ENA				0x0010
#define MADERA_MICB2B_ENA_MASK				0x0010
#define MADERA_MICB2B_ENA_SHIFT				     4
#define MADERA_MICB2B_ENA_WIDTH				     1
#define MADERA_MICB2A_ENA				0x0001
#define MADERA_MICB2A_ENA_MASK				0x0001
#define MADERA_MICB2A_ENA_SHIFT				     0
#define MADERA_MICB2A_ENA_WIDTH				     1

/* (0x0225) - HP Ctrl 1L */
#define MADERA_RMV_SHRT_HP1L				0x4000
#define MADERA_RMV_SHRT_HP1L_MASK			0x4000
#define MADERA_RMV_SHRT_HP1L_SHIFT			    14
#define MADERA_RMV_SHRT_HP1L_WIDTH			     1
#define MADERA_HP1L_FLWR				0x0004
#define MADERA_HP1L_FLWR_MASK				0x0004
#define MADERA_HP1L_FLWR_SHIFT				     2
#define MADERA_HP1L_FLWR_WIDTH				     1
#define MADERA_HP1L_SHRTI				0x0002
#define MADERA_HP1L_SHRTI_MASK				0x0002
#define MADERA_HP1L_SHRTI_SHIFT				     1
#define MADERA_HP1L_SHRTI_WIDTH				     1
#define MADERA_HP1L_SHRTO				0x0001
#define MADERA_HP1L_SHRTO_MASK				0x0001
#define MADERA_HP1L_SHRTO_SHIFT				     0
#define MADERA_HP1L_SHRTO_WIDTH				     1

/* (0x0226) - HP Ctrl 1R */
#define MADERA_RMV_SHRT_HP1R				0x4000
#define MADERA_RMV_SHRT_HP1R_MASK			0x4000
#define MADERA_RMV_SHRT_HP1R_SHIFT			    14
#define MADERA_RMV_SHRT_HP1R_WIDTH			     1
#define MADERA_HP1R_FLWR				0x0004
#define MADERA_HP1R_FLWR_MASK				0x0004
#define MADERA_HP1R_FLWR_SHIFT				     2
#define MADERA_HP1R_FLWR_WIDTH				     1
#define MADERA_HP1R_SHRTI				0x0002
#define MADERA_HP1R_SHRTI_MASK				0x0002
#define MADERA_HP1R_SHRTI_SHIFT				     1
#define MADERA_HP1R_SHRTI_WIDTH				     1
#define MADERA_HP1R_SHRTO				0x0001
#define MADERA_HP1R_SHRTO_MASK				0x0001
#define MADERA_HP1R_SHRTO_SHIFT				     0
#define MADERA_HP1R_SHRTO_WIDTH				     1

/* (0x0293)  Accessory_Detect_Mode_1 */
#define MADERA_ACCDET_SRC				0x2000
#define MADERA_ACCDET_SRC_MASK				0x2000
#define MADERA_ACCDET_SRC_SHIFT				    13
#define MADERA_ACCDET_SRC_WIDTH				     1
#define MADERA_ACCDET_POLARITY_INV_ENA			0x0080
#define MADERA_ACCDET_POLARITY_INV_ENA_MASK		0x0080
#define MADERA_ACCDET_POLARITY_INV_ENA_SHIFT		     7
#define MADERA_ACCDET_POLARITY_INV_ENA_WIDTH		     1
#define MADERA_ACCDET_MODE_MASK				0x0007
#define MADERA_ACCDET_MODE_SHIFT			     0
#define MADERA_ACCDET_MODE_WIDTH			     3

/* (0x0299)  Headphone_Detect_0 */
#define MADERA_HPD_GND_SEL				0x0007
#define MADERA_HPD_GND_SEL_MASK				0x0007
#define MADERA_HPD_GND_SEL_SHIFT			     0
#define MADERA_HPD_GND_SEL_WIDTH			     3
#define MADERA_HPD_SENSE_SEL				0x00F0
#define MADERA_HPD_SENSE_SEL_MASK			0x00F0
#define MADERA_HPD_SENSE_SEL_SHIFT			     4
#define MADERA_HPD_SENSE_SEL_WIDTH			     4
#define MADERA_HPD_FRC_SEL				0x0F00
#define MADERA_HPD_FRC_SEL_MASK				0x0F00
#define MADERA_HPD_FRC_SEL_SHIFT			     8
#define MADERA_HPD_FRC_SEL_WIDTH			     4
#define MADERA_HPD_OUT_SEL				0x7000
#define MADERA_HPD_OUT_SEL_MASK				0x7000
#define MADERA_HPD_OUT_SEL_SHIFT			    12
#define MADERA_HPD_OUT_SEL_WIDTH			     3
#define MADERA_HPD_OVD_ENA_SEL				0x8000
#define MADERA_HPD_OVD_ENA_SEL_MASK			0x8000
#define MADERA_HPD_OVD_ENA_SEL_SHIFT			    15
#define MADERA_HPD_OVD_ENA_SEL_WIDTH			     1

/* (0x029B)  Headphone_Detect_1 */
#define MADERA_HP_IMPEDANCE_RANGE_MASK			0x0600
#define MADERA_HP_IMPEDANCE_RANGE_SHIFT			     9
#define MADERA_HP_IMPEDANCE_RANGE_WIDTH			     2
#define MADERA_HP_STEP_SIZE				0x0100
#define MADERA_HP_STEP_SIZE_MASK			0x0100
#define MADERA_HP_STEP_SIZE_SHIFT			     8
#define MADERA_HP_STEP_SIZE_WIDTH			     1
#define MADERA_HP_CLK_DIV_MASK				0x0018
#define MADERA_HP_CLK_DIV_SHIFT				     3
#define MADERA_HP_CLK_DIV_WIDTH				     2
#define MADERA_HP_RATE_MASK				0x0006
#define MADERA_HP_RATE_SHIFT				     1
#define MADERA_HP_RATE_WIDTH				     2
#define MADERA_HP_POLL					0x0001
#define MADERA_HP_POLL_MASK				0x0001
#define MADERA_HP_POLL_SHIFT				     0
#define MADERA_HP_POLL_WIDTH				     1

/* (0x029C)  Headphone_Detect_2 */
#define MADERA_HP_DONE_MASK				0x8000
#define MADERA_HP_DONE_SHIFT				    15
#define MADERA_HP_DONE_WIDTH				     1
#define MADERA_HP_LVL_MASK				0x7FFF
#define MADERA_HP_LVL_SHIFT				     0
#define MADERA_HP_LVL_WIDTH				    15

/* (0x029D)  Headphone_Detect_3 */
#define MADERA_HP_DACVAL_MASK				0x03FF
#define MADERA_HP_DACVAL_SHIFT				     0
#define MADERA_HP_DACVAL_WIDTH				    10

/* (0x029F) - Headphone Detect 5 */
#define MADERA_HP_DACVAL_DOWN_MASK			0x03FF
#define MADERA_HP_DACVAL_DOWN_SHIFT			     0
#define MADERA_HP_DACVAL_DOWN_WIDTH			    10

/* (0x02A2)  Mic_Detect_1_Control_0 */
#define MADERA_MICD1_GND_MASK				0x0007
#define MADERA_MICD1_GND_SHIFT				     0
#define MADERA_MICD1_GND_WIDTH				     3
#define MADERA_MICD1_SENSE_MASK				0x00F0
#define MADERA_MICD1_SENSE_SHIFT			     4
#define MADERA_MICD1_SENSE_WIDTH			     4
#define MADERA_MICD1_ADC_MODE_MASK			0x8000
#define MADERA_MICD1_ADC_MODE_SHIFT			    15
#define MADERA_MICD1_ADC_MODE_WIDTH			     1

/* (0x02A3)  Mic_Detect_1_Control_1 */
#define MADERA_MICD_BIAS_STARTTIME_MASK			0xF000
#define MADERA_MICD_BIAS_STARTTIME_SHIFT		    12
#define MADERA_MICD_BIAS_STARTTIME_WIDTH		     4
#define MADERA_MICD_RATE_MASK				0x0F00
#define MADERA_MICD_RATE_SHIFT				     8
#define MADERA_MICD_RATE_WIDTH				     4
#define MADERA_MICD_BIAS_SRC_MASK			0x00F0
#define MADERA_MICD_BIAS_SRC_SHIFT			     4
#define MADERA_MICD_BIAS_SRC_WIDTH			     4
#define MADERA_MICD_DBTIME				0x0002
#define MADERA_MICD_DBTIME_MASK				0x0002
#define MADERA_MICD_DBTIME_SHIFT			     1
#define MADERA_MICD_DBTIME_WIDTH			     1
#define MADERA_MICD_ENA					0x0001
#define MADERA_MICD_ENA_MASK				0x0001
#define MADERA_MICD_ENA_SHIFT				     0
#define MADERA_MICD_ENA_WIDTH				     1

/* (0x02A4)  Mic_Detect_1_Control_2 */
#define MADERA_MICD_LVL_SEL_MASK			0x00FF
#define MADERA_MICD_LVL_SEL_SHIFT			     0
#define MADERA_MICD_LVL_SEL_WIDTH			     8

/* (0x02A5)  Mic_Detect_1_Control_3 */
#define MADERA_MICD_LVL_0				0x0004
#define MADERA_MICD_LVL_1				0x0008
#define MADERA_MICD_LVL_2				0x0010
#define MADERA_MICD_LVL_3				0x0020
#define MADERA_MICD_LVL_4				0x0040
#define MADERA_MICD_LVL_5				0x0080
#define MADERA_MICD_LVL_6				0x0100
#define MADERA_MICD_LVL_7				0x0200
#define MADERA_MICD_LVL_8				0x0400
#define MADERA_MICD_LVL_MASK				0x07FC
#define MADERA_MICD_LVL_SHIFT				     2
#define MADERA_MICD_LVL_WIDTH				     9
#define MADERA_MICD_VALID				0x0002
#define MADERA_MICD_VALID_MASK				0x0002
#define MADERA_MICD_VALID_SHIFT				     1
#define MADERA_MICD_VALID_WIDTH				     1
#define MADERA_MICD_STS					0x0001
#define MADERA_MICD_STS_MASK				0x0001
#define MADERA_MICD_STS_SHIFT				     0
#define MADERA_MICD_STS_WIDTH				     1

/* (0x02AB)  Mic_Detect_1_Control_4 */
#define MADERA_MICDET_ADCVAL_DIFF_MASK			0xFF00
#define MADERA_MICDET_ADCVAL_DIFF_SHIFT			     8
#define MADERA_MICDET_ADCVAL_DIFF_WIDTH			     8
#define MADERA_MICDET_ADCVAL_MASK			0x007F
#define MADERA_MICDET_ADCVAL_SHIFT			     0
#define MADERA_MICDET_ADCVAL_WIDTH			     7

/* (0x02C6)  Micd_Clamp_control */
#define MADERA_MICD_CLAMP_OVD				0x0010
#define MADERA_MICD_CLAMP_OVD_MASK			0x0010
#define MADERA_MICD_CLAMP_OVD_SHIFT			     4
#define MADERA_MICD_CLAMP_OVD_WIDTH			     1
#define MADERA_MICD_CLAMP_MODE_MASK			0x000F
#define MADERA_MICD_CLAMP_MODE_SHIFT			     0
#define MADERA_MICD_CLAMP_MODE_WIDTH			     4

/* (0x02C8)  GP_Switch_1 */
#define MADERA_SW2_MODE_MASK				0x000C
#define MADERA_SW2_MODE_SHIFT				     2
#define MADERA_SW2_MODE_WIDTH				     2
#define MADERA_SW1_MODE_MASK				0x0003
#define MADERA_SW1_MODE_SHIFT				     0
#define MADERA_SW1_MODE_WIDTH				     2

/* (0x02D3)  Jack_detect_analogue */
#define MADERA_JD2_ENA					0x0002
#define MADERA_JD2_ENA_MASK				0x0002
#define MADERA_JD2_ENA_SHIFT				     1
#define MADERA_JD2_ENA_WIDTH				     1
#define MADERA_JD1_ENA					0x0001
#define MADERA_JD1_ENA_MASK				0x0001
#define MADERA_JD1_ENA_SHIFT				     0
#define MADERA_JD1_ENA_WIDTH				     1

/* (0x0300)  Input_Enables */
#define MADERA_IN6L_ENA					0x0800
#define MADERA_IN6L_ENA_MASK				0x0800
#define MADERA_IN6L_ENA_SHIFT				    11
#define MADERA_IN6L_ENA_WIDTH				     1
#define MADERA_IN6R_ENA					0x0400
#define MADERA_IN6R_ENA_MASK				0x0400
#define MADERA_IN6R_ENA_SHIFT				    10
#define MADERA_IN6R_ENA_WIDTH				     1
#define MADERA_IN5L_ENA					0x0200
#define MADERA_IN5L_ENA_MASK				0x0200
#define MADERA_IN5L_ENA_SHIFT				     9
#define MADERA_IN5L_ENA_WIDTH				     1
#define MADERA_IN5R_ENA					0x0100
#define MADERA_IN5R_ENA_MASK				0x0100
#define MADERA_IN5R_ENA_SHIFT				     8
#define MADERA_IN5R_ENA_WIDTH				     1
#define MADERA_IN4L_ENA					0x0080
#define MADERA_IN4L_ENA_MASK				0x0080
#define MADERA_IN4L_ENA_SHIFT				     7
#define MADERA_IN4L_ENA_WIDTH				     1
#define MADERA_IN4R_ENA					0x0040
#define MADERA_IN4R_ENA_MASK				0x0040
#define MADERA_IN4R_ENA_SHIFT				     6
#define MADERA_IN4R_ENA_WIDTH				     1
#define MADERA_IN3L_ENA					0x0020
#define MADERA_IN3L_ENA_MASK				0x0020
#define MADERA_IN3L_ENA_SHIFT				     5
#define MADERA_IN3L_ENA_WIDTH				     1
#define MADERA_IN3R_ENA					0x0010
#define MADERA_IN3R_ENA_MASK				0x0010
#define MADERA_IN3R_ENA_SHIFT				     4
#define MADERA_IN3R_ENA_WIDTH				     1
#define MADERA_IN2L_ENA					0x0008
#define MADERA_IN2L_ENA_MASK				0x0008
#define MADERA_IN2L_ENA_SHIFT				     3
#define MADERA_IN2L_ENA_WIDTH				     1
#define MADERA_IN2R_ENA					0x0004
#define MADERA_IN2R_ENA_MASK				0x0004
#define MADERA_IN2R_ENA_SHIFT				     2
#define MADERA_IN2R_ENA_WIDTH				     1
#define MADERA_IN1L_ENA					0x0002
#define MADERA_IN1L_ENA_MASK				0x0002
#define MADERA_IN1L_ENA_SHIFT				     1
#define MADERA_IN1L_ENA_WIDTH				     1
#define MADERA_IN1R_ENA					0x0001
#define MADERA_IN1R_ENA_MASK				0x0001
#define MADERA_IN1R_ENA_SHIFT				     0
#define MADERA_IN1R_ENA_WIDTH				     1

/* (0x0308)  Input_Rate */
#define MADERA_IN_RATE_MASK				0xF800
#define MADERA_IN_RATE_SHIFT				    11
#define MADERA_IN_RATE_WIDTH				     5
#define MADERA_IN_MODE_MASK				0x0400
#define MADERA_IN_MODE_SHIFT				    10
#define MADERA_IN_MODE_WIDTH				     1

/* (0x0309)  Input_Volume_Ramp */
#define MADERA_IN_VD_RAMP_MASK				0x0070
#define MADERA_IN_VD_RAMP_SHIFT				     4
#define MADERA_IN_VD_RAMP_WIDTH				     3
#define MADERA_IN_VI_RAMP_MASK				0x0007
#define MADERA_IN_VI_RAMP_SHIFT				     0
#define MADERA_IN_VI_RAMP_WIDTH				     3

/* (0x030C)  HPF_Control */
#define MADERA_IN_HPF_CUT_MASK				0x0007
#define MADERA_IN_HPF_CUT_SHIFT				     0
#define MADERA_IN_HPF_CUT_WIDTH				     3

/* (0x0310)  IN1L_Control */
#define MADERA_IN1L_HPF_MASK				0x8000
#define MADERA_IN1L_HPF_SHIFT				    15
#define MADERA_IN1L_HPF_WIDTH				     1
#define MADERA_IN1_DMIC_SUP_MASK			0x1800
#define MADERA_IN1_DMIC_SUP_SHIFT			    11
#define MADERA_IN1_DMIC_SUP_WIDTH			     2
#define MADERA_IN1_MODE_MASK				0x0400
#define MADERA_IN1_MODE_SHIFT				    10
#define MADERA_IN1_MODE_WIDTH				     1
#define MADERA_IN1L_PGA_VOL_MASK			0x00FE
#define MADERA_IN1L_PGA_VOL_SHIFT			     1
#define MADERA_IN1L_PGA_VOL_WIDTH			     7

/* (0x0311)  ADC_Digital_Volume_1L */
#define MADERA_IN1L_SRC_MASK				0x4000
#define MADERA_IN1L_SRC_SHIFT				    14
#define MADERA_IN1L_SRC_WIDTH				     1
#define MADERA_IN1L_SRC_SE_MASK				0x2000
#define MADERA_IN1L_SRC_SE_SHIFT			    13
#define MADERA_IN1L_SRC_SE_WIDTH			     1
#define MADERA_IN1L_LP_MODE				0x0800
#define MADERA_IN1L_LP_MODE_MASK			0x0800
#define MADERA_IN1L_LP_MODE_SHIFT			    11
#define MADERA_IN1L_LP_MODE_WIDTH			     1
#define MADERA_IN_VU					0x0200
#define MADERA_IN_VU_MASK				0x0200
#define MADERA_IN_VU_SHIFT				     9
#define MADERA_IN_VU_WIDTH				     1
#define MADERA_IN1L_MUTE				0x0100
#define MADERA_IN1L_MUTE_MASK				0x0100
#define MADERA_IN1L_MUTE_SHIFT				     8
#define MADERA_IN1L_MUTE_WIDTH				     1
#define MADERA_IN1L_DIG_VOL_MASK			0x00FF
#define MADERA_IN1L_DIG_VOL_SHIFT			     0
#define MADERA_IN1L_DIG_VOL_WIDTH			     8

/* (0x0312)  DMIC1L_Control */
#define MADERA_IN1_OSR_MASK				0x0700
#define MADERA_IN1_OSR_SHIFT				     8
#define MADERA_IN1_OSR_WIDTH				     3

/* (0x0313)  IN1L_Rate_Control */
#define MADERA_IN1L_RATE_MASK				0xF800
#define MADERA_IN1L_RATE_SHIFT				    11
#define MADERA_IN1L_RATE_WIDTH				     5

/* (0x0314)  IN1R_Control */
#define MADERA_IN1R_HPF_MASK				0x8000
#define MADERA_IN1R_HPF_SHIFT				    15
#define MADERA_IN1R_HPF_WIDTH				     1
#define MADERA_IN1R_PGA_VOL_MASK			0x00FE
#define MADERA_IN1R_PGA_VOL_SHIFT			     1
#define MADERA_IN1R_PGA_VOL_WIDTH			     7
#define MADERA_IN1_DMICCLK_SRC_MASK			0x1800
#define MADERA_IN1_DMICCLK_SRC_SHIFT			    11
#define MADERA_IN1_DMICCLK_SRC_WIDTH			     2

/* (0x0315)  ADC_Digital_Volume_1R */
#define MADERA_IN1R_SRC_MASK				0x4000
#define MADERA_IN1R_SRC_SHIFT				    14
#define MADERA_IN1R_SRC_WIDTH				     1
#define MADERA_IN1R_SRC_SE_MASK				0x2000
#define MADERA_IN1R_SRC_SE_SHIFT			    13
#define MADERA_IN1R_SRC_SE_WIDTH			     1
#define MADERA_IN1R_LP_MODE				0x0800
#define MADERA_IN1R_LP_MODE_MASK			0x0800
#define MADERA_IN1R_LP_MODE_SHIFT			    11
#define MADERA_IN1R_LP_MODE_WIDTH			     1
#define MADERA_IN1R_MUTE				0x0100
#define MADERA_IN1R_MUTE_MASK				0x0100
#define MADERA_IN1R_MUTE_SHIFT				     8
#define MADERA_IN1R_MUTE_WIDTH				     1
#define MADERA_IN1R_DIG_VOL_MASK			0x00FF
#define MADERA_IN1R_DIG_VOL_SHIFT			     0
#define MADERA_IN1R_DIG_VOL_WIDTH			     8

/* (0x0317)  IN1R_Rate_Control */
#define MADERA_IN1R_RATE_MASK				0xF800
#define MADERA_IN1R_RATE_SHIFT				    11
#define MADERA_IN1R_RATE_WIDTH				     5

/* (0x0318)  IN2L_Control */
#define MADERA_IN2L_HPF_MASK				0x8000
#define MADERA_IN2L_HPF_SHIFT				    15
#define MADERA_IN2L_HPF_WIDTH				     1
#define MADERA_IN2_DMIC_SUP_MASK			0x1800
#define MADERA_IN2_DMIC_SUP_SHIFT			    11
#define MADERA_IN2_DMIC_SUP_WIDTH			     2
#define MADERA_IN2_MODE_MASK				0x0400
#define MADERA_IN2_MODE_SHIFT				    10
#define MADERA_IN2_MODE_WIDTH				     1
#define MADERA_IN2L_PGA_VOL_MASK			0x00FE
#define MADERA_IN2L_PGA_VOL_SHIFT			     1
#define MADERA_IN2L_PGA_VOL_WIDTH			     7

/* (0x0319)  ADC_Digital_Volume_2L */
#define MADERA_IN2L_SRC_MASK				0x4000
#define MADERA_IN2L_SRC_SHIFT				    14
#define MADERA_IN2L_SRC_WIDTH				     1
#define MADERA_IN2L_SRC_SE_MASK				0x2000
#define MADERA_IN2L_SRC_SE_SHIFT			    13
#define MADERA_IN2L_SRC_SE_WIDTH			     1
#define MADERA_IN2L_LP_MODE				0x0800
#define MADERA_IN2L_LP_MODE_MASK			0x0800
#define MADERA_IN2L_LP_MODE_SHIFT			    11
#define MADERA_IN2L_LP_MODE_WIDTH			     1
#define MADERA_IN2L_MUTE				0x0100
#define MADERA_IN2L_MUTE_MASK				0x0100
#define MADERA_IN2L_MUTE_SHIFT				     8
#define MADERA_IN2L_MUTE_WIDTH				     1
#define MADERA_IN2L_DIG_VOL_MASK			0x00FF
#define MADERA_IN2L_DIG_VOL_SHIFT			     0
#define MADERA_IN2L_DIG_VOL_WIDTH			     8

/* (0x031A)  DMIC2L_Control */
#define MADERA_IN2_OSR_MASK				0x0700
#define MADERA_IN2_OSR_SHIFT				     8
#define MADERA_IN2_OSR_WIDTH				     3

/* (0x031C)  IN2R_Control */
#define MADERA_IN2R_HPF_MASK				0x8000
#define MADERA_IN2R_HPF_SHIFT				    15
#define MADERA_IN2R_HPF_WIDTH				     1
#define MADERA_IN2R_PGA_VOL_MASK			0x00FE
#define MADERA_IN2R_PGA_VOL_SHIFT			     1
#define MADERA_IN2R_PGA_VOL_WIDTH			     7
#define MADERA_IN2_DMICCLK_SRC_MASK			0x1800
#define MADERA_IN2_DMICCLK_SRC_SHIFT			    11
#define MADERA_IN2_DMICCLK_SRC_WIDTH			     2

/* (0x031D)  ADC_Digital_Volume_2R */
#define MADERA_IN2R_SRC_MASK				0x4000
#define MADERA_IN2R_SRC_SHIFT				    14
#define MADERA_IN2R_SRC_WIDTH				     1
#define MADERA_IN2R_SRC_SE_MASK				0x2000
#define MADERA_IN2R_SRC_SE_SHIFT			    13
#define MADERA_IN2R_SRC_SE_WIDTH			     1
#define MADERA_IN2R_LP_MODE				0x0800
#define MADERA_IN2R_LP_MODE_MASK			0x0800
#define MADERA_IN2R_LP_MODE_SHIFT			    11
#define MADERA_IN2R_LP_MODE_WIDTH			     1
#define MADERA_IN2R_MUTE				0x0100
#define MADERA_IN2R_MUTE_MASK				0x0100
#define MADERA_IN2R_MUTE_SHIFT				     8
#define MADERA_IN2R_MUTE_WIDTH				     1
#define MADERA_IN2R_DIG_VOL_MASK			0x00FF
#define MADERA_IN2R_DIG_VOL_SHIFT			     0
#define MADERA_IN2R_DIG_VOL_WIDTH			     8

/* (0x0320)  IN3L_Control */
#define MADERA_IN3L_HPF_MASK				0x8000
#define MADERA_IN3L_HPF_SHIFT				    15
#define MADERA_IN3L_HPF_WIDTH				     1
#define MADERA_IN3_DMIC_SUP_MASK			0x1800
#define MADERA_IN3_DMIC_SUP_SHIFT			    11
#define MADERA_IN3_DMIC_SUP_WIDTH			     2
#define MADERA_IN3_MODE_MASK				0x0400
#define MADERA_IN3_MODE_SHIFT				    10
#define MADERA_IN3_MODE_WIDTH				     1
#define MADERA_IN3L_PGA_VOL_MASK			0x00FE
#define MADERA_IN3L_PGA_VOL_SHIFT			     1
#define MADERA_IN3L_PGA_VOL_WIDTH			     7

/* (0x0321)  ADC_Digital_Volume_3L */
#define MADERA_IN3L_MUTE				0x0100
#define MADERA_IN3L_MUTE_MASK				0x0100
#define MADERA_IN3L_MUTE_SHIFT				     8
#define MADERA_IN3L_MUTE_WIDTH				     1
#define MADERA_IN3L_DIG_VOL_MASK			0x00FF
#define MADERA_IN3L_DIG_VOL_SHIFT			     0
#define MADERA_IN3L_DIG_VOL_WIDTH			     8

/* (0x0322)  DMIC3L_Control */
#define MADERA_IN3_OSR_MASK				0x0700
#define MADERA_IN3_OSR_SHIFT				     8
#define MADERA_IN3_OSR_WIDTH				     3

/* (0x0324)  IN3R_Control */
#define MADERA_IN3R_HPF_MASK				0x8000
#define MADERA_IN3R_HPF_SHIFT				    15
#define MADERA_IN3R_HPF_WIDTH				     1
#define MADERA_IN3R_PGA_VOL_MASK			0x00FE
#define MADERA_IN3R_PGA_VOL_SHIFT			     1
#define MADERA_IN3R_PGA_VOL_WIDTH			     7
#define MADERA_IN3_DMICCLK_SRC_MASK			0x1800
#define MADERA_IN3_DMICCLK_SRC_SHIFT			    11
#define MADERA_IN3_DMICCLK_SRC_WIDTH			     2

/* (0x0325)  ADC_Digital_Volume_3R */
#define MADERA_IN3R_MUTE				0x0100
#define MADERA_IN3R_MUTE_MASK				0x0100
#define MADERA_IN3R_MUTE_SHIFT				     8
#define MADERA_IN3R_MUTE_WIDTH				     1
#define MADERA_IN3R_DIG_VOL_MASK			0x00FF
#define MADERA_IN3R_DIG_VOL_SHIFT			     0
#define MADERA_IN3R_DIG_VOL_WIDTH			     8

/* (0x0328)  IN4L_Control */
#define MADERA_IN4L_HPF_MASK				0x8000
#define MADERA_IN4L_HPF_SHIFT				    15
#define MADERA_IN4L_HPF_WIDTH				     1
#define MADERA_IN4_DMIC_SUP_MASK			0x1800
#define MADERA_IN4_DMIC_SUP_SHIFT			    11
#define MADERA_IN4_DMIC_SUP_WIDTH			     2

/* (0x0329)  ADC_Digital_Volume_4L */
#define MADERA_IN4L_MUTE				0x0100
#define MADERA_IN4L_MUTE_MASK				0x0100
#define MADERA_IN4L_MUTE_SHIFT				     8
#define MADERA_IN4L_MUTE_WIDTH				     1
#define MADERA_IN4L_DIG_VOL_MASK			0x00FF
#define MADERA_IN4L_DIG_VOL_SHIFT			     0
#define MADERA_IN4L_DIG_VOL_WIDTH			     8

/* (0x032A)  DMIC4L_Control */
#define MADERA_IN4_OSR_MASK				0x0700
#define MADERA_IN4_OSR_SHIFT				     8
#define MADERA_IN4_OSR_WIDTH				     3

/* (0x032C)  IN4R_Control */
#define MADERA_IN4R_HPF_MASK				0x8000
#define MADERA_IN4R_HPF_SHIFT				    15
#define MADERA_IN4R_HPF_WIDTH				     1
#define MADERA_IN4_DMICCLK_SRC_MASK			0x1800
#define MADERA_IN4_DMICCLK_SRC_SHIFT			    11
#define MADERA_IN4_DMICCLK_SRC_WIDTH			     2

/* (0x032D)  ADC_Digital_Volume_4R */
#define MADERA_IN4R_MUTE				0x0100
#define MADERA_IN4R_MUTE_MASK				0x0100
#define MADERA_IN4R_MUTE_SHIFT				     8
#define MADERA_IN4R_MUTE_WIDTH				     1
#define MADERA_IN4R_DIG_VOL_MASK			0x00FF
#define MADERA_IN4R_DIG_VOL_SHIFT			     0
#define MADERA_IN4R_DIG_VOL_WIDTH			     8

/* (0x0330)  IN5L_Control */
#define MADERA_IN5L_HPF_MASK				0x8000
#define MADERA_IN5L_HPF_SHIFT				    15
#define MADERA_IN5L_HPF_WIDTH				     1
#define MADERA_IN5_DMIC_SUP_MASK			0x1800
#define MADERA_IN5_DMIC_SUP_SHIFT			    11
#define MADERA_IN5_DMIC_SUP_WIDTH			     2

/* (0x0331)  ADC_Digital_Volume_5L */
#define MADERA_IN5L_MUTE				0x0100
#define MADERA_IN5L_MUTE_MASK				0x0100
#define MADERA_IN5L_MUTE_SHIFT				     8
#define MADERA_IN5L_MUTE_WIDTH				     1
#define MADERA_IN5L_DIG_VOL_MASK			0x00FF
#define MADERA_IN5L_DIG_VOL_SHIFT			     0
#define MADERA_IN5L_DIG_VOL_WIDTH			     8

/* (0x0332)  DMIC5L_Control */
#define MADERA_IN5_OSR_MASK				0x0700
#define MADERA_IN5_OSR_SHIFT				     8
#define MADERA_IN5_OSR_WIDTH				     3

/* (0x0334)  IN5R_Control */
#define MADERA_IN5R_HPF_MASK				0x8000
#define MADERA_IN5R_HPF_SHIFT				    15
#define MADERA_IN5R_HPF_WIDTH				     1
#define MADERA_IN5_DMICCLK_SRC_MASK			0x1800
#define MADERA_IN5_DMICCLK_SRC_SHIFT			    11
#define MADERA_IN5_DMICCLK_SRC_WIDTH			     2

/* (0x0335)  ADC_Digital_Volume_5R */
#define MADERA_IN5R_MUTE				0x0100
#define MADERA_IN5R_MUTE_MASK				0x0100
#define MADERA_IN5R_MUTE_SHIFT				     8
#define MADERA_IN5R_MUTE_WIDTH				     1
#define MADERA_IN5R_DIG_VOL_MASK			0x00FF
#define MADERA_IN5R_DIG_VOL_SHIFT			     0
#define MADERA_IN5R_DIG_VOL_WIDTH			     8

/* (0x0338)  IN6L_Control */
#define MADERA_IN6L_HPF_MASK				0x8000
#define MADERA_IN6L_HPF_SHIFT				    15
#define MADERA_IN6L_HPF_WIDTH				     1
#define MADERA_IN6_DMIC_SUP_MASK			0x1800
#define MADERA_IN6_DMIC_SUP_SHIFT			    11
#define MADERA_IN6_DMIC_SUP_WIDTH			     2

/* (0x0339)  ADC_Digital_Volume_6L */
#define MADERA_IN6L_MUTE				0x0100
#define MADERA_IN6L_MUTE_MASK				0x0100
#define MADERA_IN6L_MUTE_SHIFT				     8
#define MADERA_IN6L_MUTE_WIDTH				     1
#define MADERA_IN6L_DIG_VOL_MASK			0x00FF
#define MADERA_IN6L_DIG_VOL_SHIFT			     0
#define MADERA_IN6L_DIG_VOL_WIDTH			     8

/* (0x033A)  DMIC6L_Control */
#define MADERA_IN6_OSR_MASK				0x0700
#define MADERA_IN6_OSR_SHIFT				     8
#define MADERA_IN6_OSR_WIDTH				     3

/* (0x033C)  IN6R_Control */
#define MADERA_IN6R_HPF_MASK				0x8000
#define MADERA_IN6R_HPF_SHIFT				    15
#define MADERA_IN6R_HPF_WIDTH				     1

/* (0x033D)  ADC_Digital_Volume_6R */
#define MADERA_IN6R_MUTE				0x0100
#define MADERA_IN6R_MUTE_MASK				0x0100
#define MADERA_IN6R_MUTE_SHIFT				     8
#define MADERA_IN6R_MUTE_WIDTH				     1
#define MADERA_IN6R_DIG_VOL_MASK			0x00FF
#define MADERA_IN6R_DIG_VOL_SHIFT			     0
#define MADERA_IN6R_DIG_VOL_WIDTH			     8

/* (0x033E)  DMIC6R_Control */
#define MADERA_IN6_DMICCLK_SRC_MASK			0x1800
#define MADERA_IN6_DMICCLK_SRC_SHIFT			    11
#define MADERA_IN6_DMICCLK_SRC_WIDTH			     2

/* (0x0400)  Output_Enables_1 */
#define MADERA_EP_SEL					0x8000
#define MADERA_EP_SEL_MASK				0x8000
#define MADERA_EP_SEL_SHIFT				    15
#define MADERA_EP_SEL_WIDTH				     1
#define MADERA_OUT6L_ENA				0x0800
#define MADERA_OUT6L_ENA_MASK				0x0800
#define MADERA_OUT6L_ENA_SHIFT				    11
#define MADERA_OUT6L_ENA_WIDTH				     1
#define MADERA_OUT6R_ENA				0x0400
#define MADERA_OUT6R_ENA_MASK				0x0400
#define MADERA_OUT6R_ENA_SHIFT				    10
#define MADERA_OUT6R_ENA_WIDTH				     1
#define MADERA_OUT5L_ENA				0x0200
#define MADERA_OUT5L_ENA_MASK				0x0200
#define MADERA_OUT5L_ENA_SHIFT				     9
#define MADERA_OUT5L_ENA_WIDTH				     1
#define MADERA_OUT5R_ENA				0x0100
#define MADERA_OUT5R_ENA_MASK				0x0100
#define MADERA_OUT5R_ENA_SHIFT				     8
#define MADERA_OUT5R_ENA_WIDTH				     1
#define MADERA_OUT4L_ENA				0x0080
#define MADERA_OUT4L_ENA_MASK				0x0080
#define MADERA_OUT4L_ENA_SHIFT				     7
#define MADERA_OUT4L_ENA_WIDTH				     1
#define MADERA_OUT4R_ENA				0x0040
#define MADERA_OUT4R_ENA_MASK				0x0040
#define MADERA_OUT4R_ENA_SHIFT				     6
#define MADERA_OUT4R_ENA_WIDTH				     1
#define MADERA_OUT3L_ENA				0x0020
#define MADERA_OUT3L_ENA_MASK				0x0020
#define MADERA_OUT3L_ENA_SHIFT				     5
#define MADERA_OUT3L_ENA_WIDTH				     1
#define MADERA_OUT3R_ENA				0x0010
#define MADERA_OUT3R_ENA_MASK				0x0010
#define MADERA_OUT3R_ENA_SHIFT				     4
#define MADERA_OUT3R_ENA_WIDTH				     1
#define MADERA_OUT2L_ENA				0x0008
#define MADERA_OUT2L_ENA_MASK				0x0008
#define MADERA_OUT2L_ENA_SHIFT				     3
#define MADERA_OUT2L_ENA_WIDTH				     1
#define MADERA_OUT2R_ENA				0x0004
#define MADERA_OUT2R_ENA_MASK				0x0004
#define MADERA_OUT2R_ENA_SHIFT				     2
#define MADERA_OUT2R_ENA_WIDTH				     1
#define MADERA_OUT1L_ENA				0x0002
#define MADERA_OUT1L_ENA_MASK				0x0002
#define MADERA_OUT1L_ENA_SHIFT				     1
#define MADERA_OUT1L_ENA_WIDTH				     1
#define MADERA_OUT1R_ENA				0x0001
#define MADERA_OUT1R_ENA_MASK				0x0001
#define MADERA_OUT1R_ENA_SHIFT				     0
#define MADERA_OUT1R_ENA_WIDTH				     1

/* (0x0408)  Output_Rate_1 */
#define MADERA_CP_DAC_MODE_MASK				0x0040
#define MADERA_CP_DAC_MODE_SHIFT			     6
#define MADERA_CP_DAC_MODE_WIDTH			     1
#define MADERA_OUT_EXT_CLK_DIV_MASK			0x0030
#define MADERA_OUT_EXT_CLK_DIV_SHIFT			     4
#define MADERA_OUT_EXT_CLK_DIV_WIDTH			     2
#define MADERA_OUT_CLK_SRC_MASK				0x0007
#define MADERA_OUT_CLK_SRC_SHIFT			     0
#define MADERA_OUT_CLK_SRC_WIDTH			     3

/* (0x0409)  Output_Volume_Ramp */
#define MADERA_OUT_VD_RAMP_MASK				0x0070
#define MADERA_OUT_VD_RAMP_SHIFT			     4
#define MADERA_OUT_VD_RAMP_WIDTH			     3
#define MADERA_OUT_VI_RAMP_MASK				0x0007
#define MADERA_OUT_VI_RAMP_SHIFT			     0
#define MADERA_OUT_VI_RAMP_WIDTH			     3

/* (0x0410)  Output_Path_Config_1L */
#define MADERA_OUT1_MONO				0x1000
#define MADERA_OUT1_MONO_MASK				0x1000
#define MADERA_OUT1_MONO_SHIFT				    12
#define MADERA_OUT1_MONO_WIDTH				     1
#define MADERA_OUT1L_ANC_SRC_MASK			0x0C00
#define MADERA_OUT1L_ANC_SRC_SHIFT			    10
#define MADERA_OUT1L_ANC_SRC_WIDTH			     2

/* (0x0411)  DAC_Digital_Volume_1L */
#define MADERA_OUT1L_VU					0x0200
#define MADERA_OUT1L_VU_MASK				0x0200
#define MADERA_OUT1L_VU_SHIFT				     9
#define MADERA_OUT1L_VU_WIDTH				     1
#define MADERA_OUT1L_MUTE				0x0100
#define MADERA_OUT1L_MUTE_MASK				0x0100
#define MADERA_OUT1L_MUTE_SHIFT				     8
#define MADERA_OUT1L_MUTE_WIDTH				     1
#define MADERA_OUT1L_VOL_MASK				0x00FF
#define MADERA_OUT1L_VOL_SHIFT				     0
#define MADERA_OUT1L_VOL_WIDTH				     8

/* (0x0412)  Output_Path_Config_1 */
#define MADERA_HP1_GND_SEL_MASK				0x0007
#define MADERA_HP1_GND_SEL_SHIFT			     0
#define MADERA_HP1_GND_SEL_WIDTH			     3

/* (0x0414)  Output_Path_Config_1R */
#define MADERA_OUT1R_ANC_SRC_MASK			0x0C00
#define MADERA_OUT1R_ANC_SRC_SHIFT			    10
#define MADERA_OUT1R_ANC_SRC_WIDTH			     2

/* (0x0415)  DAC_Digital_Volume_1R */
#define MADERA_OUT1R_MUTE				0x0100
#define MADERA_OUT1R_MUTE_MASK				0x0100
#define MADERA_OUT1R_MUTE_SHIFT				     8
#define MADERA_OUT1R_MUTE_WIDTH				     1
#define MADERA_OUT1R_VOL_MASK				0x00FF
#define MADERA_OUT1R_VOL_SHIFT				     0
#define MADERA_OUT1R_VOL_WIDTH				     8

/* (0x0418)  Output_Path_Config_2L */
#define MADERA_OUT2L_ANC_SRC_MASK			0x0C00
#define MADERA_OUT2L_ANC_SRC_SHIFT			    10
#define MADERA_OUT2L_ANC_SRC_WIDTH			     2

/* (0x0419)  DAC_Digital_Volume_2L */
#define MADERA_OUT2L_MUTE				0x0100
#define MADERA_OUT2L_MUTE_MASK				0x0100
#define MADERA_OUT2L_MUTE_SHIFT				     8
#define MADERA_OUT2L_MUTE_WIDTH				     1
#define MADERA_OUT2L_VOL_MASK				0x00FF
#define MADERA_OUT2L_VOL_SHIFT				     0
#define MADERA_OUT2L_VOL_WIDTH				     8

/* (0x041A)  Output_Path_Config_2 */
#define MADERA_HP2_GND_SEL_MASK				0x0007
#define MADERA_HP2_GND_SEL_SHIFT			     0
#define MADERA_HP2_GND_SEL_WIDTH			     3

/* (0x041C)  Output_Path_Config_2R */
#define MADERA_OUT2R_ANC_SRC_MASK			0x0C00
#define MADERA_OUT2R_ANC_SRC_SHIFT			    10
#define MADERA_OUT2R_ANC_SRC_WIDTH			     2

/* (0x041D)  DAC_Digital_Volume_2R */
#define MADERA_OUT2R_MUTE				0x0100
#define MADERA_OUT2R_MUTE_MASK				0x0100
#define MADERA_OUT2R_MUTE_SHIFT				     8
#define MADERA_OUT2R_MUTE_WIDTH				     1
#define MADERA_OUT2R_VOL_MASK				0x00FF
#define MADERA_OUT2R_VOL_SHIFT				     0
#define MADERA_OUT2R_VOL_WIDTH				     8

/* (0x0420)  Output_Path_Config_3L */
#define MADERA_OUT3L_ANC_SRC_MASK			0x0C00
#define MADERA_OUT3L_ANC_SRC_SHIFT			    10
#define MADERA_OUT3L_ANC_SRC_WIDTH			     2

/* (0x0421)  DAC_Digital_Volume_3L */
#define MADERA_OUT3L_MUTE				0x0100
#define MADERA_OUT3L_MUTE_MASK				0x0100
#define MADERA_OUT3L_MUTE_SHIFT				     8
#define MADERA_OUT3L_MUTE_WIDTH				     1
#define MADERA_OUT3L_VOL_MASK				0x00FF
#define MADERA_OUT3L_VOL_SHIFT				     0
#define MADERA_OUT3L_VOL_WIDTH				     8

/* (0x0424)  Output_Path_Config_3R */
#define MADERA_OUT3R_ANC_SRC_MASK			0x0C00
#define MADERA_OUT3R_ANC_SRC_SHIFT			    10
#define MADERA_OUT3R_ANC_SRC_WIDTH			     2

/* (0x0425)  DAC_Digital_Volume_3R */
#define MADERA_OUT3R_MUTE				0x0100
#define MADERA_OUT3R_MUTE_MASK				0x0100
#define MADERA_OUT3R_MUTE_SHIFT				     8
#define MADERA_OUT3R_MUTE_WIDTH				     1
#define MADERA_OUT3R_VOL_MASK				0x00FF
#define MADERA_OUT3R_VOL_SHIFT				     0
#define MADERA_OUT3R_VOL_WIDTH				     8

/* (0x0428)  Output_Path_Config_4L */
#define MADERA_OUT4L_ANC_SRC_MASK			0x0C00
#define MADERA_OUT4L_ANC_SRC_SHIFT			    10
#define MADERA_OUT4L_ANC_SRC_WIDTH			     2

/* (0x0429)  DAC_Digital_Volume_4L */
#define MADERA_OUT4L_MUTE				0x0100
#define MADERA_OUT4L_MUTE_MASK				0x0100
#define MADERA_OUT4L_MUTE_SHIFT				     8
#define MADERA_OUT4L_MUTE_WIDTH				     1
#define MADERA_OUT4L_VOL_MASK				0x00FF
#define MADERA_OUT4L_VOL_SHIFT				     0
#define MADERA_OUT4L_VOL_WIDTH				     8

/* (0x042C)  Output_Path_Config_4R */
#define MADERA_OUT4R_ANC_SRC_MASK			0x0C00
#define MADERA_OUT4R_ANC_SRC_SHIFT			    10
#define MADERA_OUT4R_ANC_SRC_WIDTH			     2

/* (0x042D)  DAC_Digital_Volume_4R */
#define MADERA_OUT4R_MUTE				0x0100
#define MADERA_OUT4R_MUTE_MASK				0x0100
#define MADERA_OUT4R_MUTE_SHIFT				     8
#define MADERA_OUT4R_MUTE_WIDTH				     1
#define MADERA_OUT4R_VOL_MASK				0x00FF
#define MADERA_OUT4R_VOL_SHIFT				     0
#define MADERA_OUT4R_VOL_WIDTH				     8

/* (0x0430)  Output_Path_Config_5L */
#define MADERA_OUT5_OSR					0x2000
#define MADERA_OUT5_OSR_MASK				0x2000
#define MADERA_OUT5_OSR_SHIFT				    13
#define MADERA_OUT5_OSR_WIDTH				     1
#define MADERA_OUT5L_ANC_SRC_MASK			0x0C00
#define MADERA_OUT5L_ANC_SRC_SHIFT			    10
#define MADERA_OUT5L_ANC_SRC_WIDTH			     2

/* (0x0431)  DAC_Digital_Volume_5L */
#define MADERA_OUT5L_MUTE				0x0100
#define MADERA_OUT5L_MUTE_MASK				0x0100
#define MADERA_OUT5L_MUTE_SHIFT				     8
#define MADERA_OUT5L_MUTE_WIDTH				     1
#define MADERA_OUT5L_VOL_MASK				0x00FF
#define MADERA_OUT5L_VOL_SHIFT				     0
#define MADERA_OUT5L_VOL_WIDTH				     8

/* (0x0434)  Output_Path_Config_5R */
#define MADERA_OUT5R_ANC_SRC_MASK			0x0C00
#define MADERA_OUT5R_ANC_SRC_SHIFT			    10
#define MADERA_OUT5R_ANC_SRC_WIDTH			     2

/* (0x0435)  DAC_Digital_Volume_5R */
#define MADERA_OUT5R_MUTE				0x0100
#define MADERA_OUT5R_MUTE_MASK				0x0100
#define MADERA_OUT5R_MUTE_SHIFT				     8
#define MADERA_OUT5R_MUTE_WIDTH				     1
#define MADERA_OUT5R_VOL_MASK				0x00FF
#define MADERA_OUT5R_VOL_SHIFT				     0
#define MADERA_OUT5R_VOL_WIDTH				     8

/* (0x0438)  Output_Path_Config_6L */
#define MADERA_OUT6_OSR					0x2000
#define MADERA_OUT6_OSR_MASK				0x2000
#define MADERA_OUT6_OSR_SHIFT				    13
#define MADERA_OUT6_OSR_WIDTH				     1
#define MADERA_OUT6L_ANC_SRC_MASK			0x0C00
#define MADERA_OUT6L_ANC_SRC_SHIFT			    10
#define MADERA_OUT6L_ANC_SRC_WIDTH			     2

/* (0x0439)  DAC_Digital_Volume_6L */
#define MADERA_OUT6L_MUTE				0x0100
#define MADERA_OUT6L_MUTE_MASK				0x0100
#define MADERA_OUT6L_MUTE_SHIFT				     8
#define MADERA_OUT6L_MUTE_WIDTH				     1
#define MADERA_OUT6L_VOL_MASK				0x00FF
#define MADERA_OUT6L_VOL_SHIFT				     0
#define MADERA_OUT6L_VOL_WIDTH				     8

/* (0x043C)  Output_Path_Config_6R */
#define MADERA_OUT6R_ANC_SRC_MASK			0x0C00
#define MADERA_OUT6R_ANC_SRC_SHIFT			    10
#define MADERA_OUT6R_ANC_SRC_WIDTH			     2

/* (0x043D)  DAC_Digital_Volume_6R */
#define MADERA_OUT6R_MUTE				0x0100
#define MADERA_OUT6R_MUTE_MASK				0x0100
#define MADERA_OUT6R_MUTE_SHIFT				     8
#define MADERA_OUT6R_MUTE_WIDTH				     1
#define MADERA_OUT6R_VOL_MASK				0x00FF
#define MADERA_OUT6R_VOL_SHIFT				     0
#define MADERA_OUT6R_VOL_WIDTH				     8

/* (0x0450) - DAC AEC Control 1 */
#define MADERA_AEC1_LOOPBACK_SRC_MASK			0x003C
#define MADERA_AEC1_LOOPBACK_SRC_SHIFT			     2
#define MADERA_AEC1_LOOPBACK_SRC_WIDTH			     4
#define MADERA_AEC1_ENA_STS				0x0002
#define MADERA_AEC1_ENA_STS_MASK			0x0002
#define MADERA_AEC1_ENA_STS_SHIFT			     1
#define MADERA_AEC1_ENA_STS_WIDTH			     1
#define MADERA_AEC1_LOOPBACK_ENA			0x0001
#define MADERA_AEC1_LOOPBACK_ENA_MASK			0x0001
#define MADERA_AEC1_LOOPBACK_ENA_SHIFT			     0
#define MADERA_AEC1_LOOPBACK_ENA_WIDTH			     1

/* (0x0451)  DAC_AEC_Control_2 */
#define MADERA_AEC2_LOOPBACK_SRC_MASK			0x003C
#define MADERA_AEC2_LOOPBACK_SRC_SHIFT			     2
#define MADERA_AEC2_LOOPBACK_SRC_WIDTH			     4
#define MADERA_AEC2_ENA_STS				0x0002
#define MADERA_AEC2_ENA_STS_MASK			0x0002
#define MADERA_AEC2_ENA_STS_SHIFT			     1
#define MADERA_AEC2_ENA_STS_WIDTH			     1
#define MADERA_AEC2_LOOPBACK_ENA			0x0001
#define MADERA_AEC2_LOOPBACK_ENA_MASK			0x0001
#define MADERA_AEC2_LOOPBACK_ENA_SHIFT			     0
#define MADERA_AEC2_LOOPBACK_ENA_WIDTH			     1

/* (0x0458)  Noise_Gate_Control */
#define MADERA_NGATE_HOLD_MASK				0x0030
#define MADERA_NGATE_HOLD_SHIFT				     4
#define MADERA_NGATE_HOLD_WIDTH				     2
#define MADERA_NGATE_THR_MASK				0x000E
#define MADERA_NGATE_THR_SHIFT				     1
#define MADERA_NGATE_THR_WIDTH				     3
#define MADERA_NGATE_ENA				0x0001
#define MADERA_NGATE_ENA_MASK				0x0001
#define MADERA_NGATE_ENA_SHIFT				     0
#define MADERA_NGATE_ENA_WIDTH				     1

/* (0x0490)  PDM_SPK1_CTRL_1 */
#define MADERA_SPK1R_MUTE				0x2000
#define MADERA_SPK1R_MUTE_MASK				0x2000
#define MADERA_SPK1R_MUTE_SHIFT				    13
#define MADERA_SPK1R_MUTE_WIDTH				     1
#define MADERA_SPK1L_MUTE				0x1000
#define MADERA_SPK1L_MUTE_MASK				0x1000
#define MADERA_SPK1L_MUTE_SHIFT				    12
#define MADERA_SPK1L_MUTE_WIDTH				     1
#define MADERA_SPK1_MUTE_ENDIAN				0x0100
#define MADERA_SPK1_MUTE_ENDIAN_MASK			0x0100
#define MADERA_SPK1_MUTE_ENDIAN_SHIFT			     8
#define MADERA_SPK1_MUTE_ENDIAN_WIDTH			     1
#define MADERA_SPK1_MUTE_SEQ1_MASK			0x00FF
#define MADERA_SPK1_MUTE_SEQ1_SHIFT			     0
#define MADERA_SPK1_MUTE_SEQ1_WIDTH			     8

/* (0x0491)  PDM_SPK1_CTRL_2 */
#define MADERA_SPK1_FMT					0x0001
#define MADERA_SPK1_FMT_MASK				0x0001
#define MADERA_SPK1_FMT_SHIFT				     0
#define MADERA_SPK1_FMT_WIDTH				     1

/* (0x0492)  PDM_SPK2_CTRL_1 */
#define MADERA_SPK2R_MUTE				0x2000
#define MADERA_SPK2R_MUTE_MASK				0x2000
#define MADERA_SPK2R_MUTE_SHIFT				    13
#define MADERA_SPK2R_MUTE_WIDTH				     1
#define MADERA_SPK2L_MUTE				0x1000
#define MADERA_SPK2L_MUTE_MASK				0x1000
#define MADERA_SPK2L_MUTE_SHIFT				    12
#define MADERA_SPK2L_MUTE_WIDTH				     1

/* (0x04A0) - HP1 Short Circuit Ctrl */
#define MADERA_HP1_SC_ENA				0x1000
#define MADERA_HP1_SC_ENA_MASK				0x1000
#define MADERA_HP1_SC_ENA_SHIFT				    12
#define MADERA_HP1_SC_ENA_WIDTH				     1

/* (0x04A1) - HP2 Short Circuit Ctrl */
#define MADERA_HP2_SC_ENA				0x1000
#define MADERA_HP2_SC_ENA_MASK				0x1000
#define MADERA_HP2_SC_ENA_SHIFT				    12
#define MADERA_HP2_SC_ENA_WIDTH				     1

/* (0x04A2) - HP3 Short Circuit Ctrl */
#define MADERA_HP3_SC_ENA				0x1000
#define MADERA_HP3_SC_ENA_MASK				0x1000
#define MADERA_HP3_SC_ENA_SHIFT				    12
#define MADERA_HP3_SC_ENA_WIDTH				     1

/* (0x04A8) - HP_Test_Ctrl_5 */
#define MADERA_HP1L_ONEFLT				0x0100
#define MADERA_HP1L_ONEFLT_MASK				0x0100
#define MADERA_HP1L_ONEFLT_SHIFT			     8
#define MADERA_HP1L_ONEFLT_WIDTH			     1

/* (0x04A9) - HP_Test_Ctrl_6 */
#define MADERA_HP1R_ONEFLT				0x0100
#define MADERA_HP1R_ONEFLT_MASK				0x0100
#define MADERA_HP1R_ONEFLT_SHIFT			     8
#define MADERA_HP1R_ONEFLT_WIDTH			     1

/* (0x0500)  AIF1_BCLK_Ctrl */
#define MADERA_AIF1_BCLK_INV				0x0080
#define MADERA_AIF1_BCLK_INV_MASK			0x0080
#define MADERA_AIF1_BCLK_INV_SHIFT			     7
#define MADERA_AIF1_BCLK_INV_WIDTH			     1
#define MADERA_AIF1_BCLK_MSTR				0x0020
#define MADERA_AIF1_BCLK_MSTR_MASK			0x0020
#define MADERA_AIF1_BCLK_MSTR_SHIFT			     5
#define MADERA_AIF1_BCLK_MSTR_WIDTH			     1
#define MADERA_AIF1_BCLK_FREQ_MASK			0x001F
#define MADERA_AIF1_BCLK_FREQ_SHIFT			     0
#define MADERA_AIF1_BCLK_FREQ_WIDTH			     5

/* (0x0501)  AIF1_Tx_Pin_Ctrl */
#define MADERA_AIF1TX_LRCLK_SRC				0x0008
#define MADERA_AIF1TX_LRCLK_SRC_MASK			0x0008
#define MADERA_AIF1TX_LRCLK_SRC_SHIFT			     3
#define MADERA_AIF1TX_LRCLK_SRC_WIDTH			     1
#define MADERA_AIF1TX_LRCLK_INV				0x0004
#define MADERA_AIF1TX_LRCLK_INV_MASK			0x0004
#define MADERA_AIF1TX_LRCLK_INV_SHIFT			     2
#define MADERA_AIF1TX_LRCLK_INV_WIDTH			     1
#define MADERA_AIF1TX_LRCLK_MSTR			0x0001
#define MADERA_AIF1TX_LRCLK_MSTR_MASK			0x0001
#define MADERA_AIF1TX_LRCLK_MSTR_SHIFT			     0
#define MADERA_AIF1TX_LRCLK_MSTR_WIDTH			     1

/* (0x0502)  AIF1_Rx_Pin_Ctrl */
#define MADERA_AIF1RX_LRCLK_INV				0x0004
#define MADERA_AIF1RX_LRCLK_INV_MASK			0x0004
#define MADERA_AIF1RX_LRCLK_INV_SHIFT			     2
#define MADERA_AIF1RX_LRCLK_INV_WIDTH			     1
#define MADERA_AIF1RX_LRCLK_FRC				0x0002
#define MADERA_AIF1RX_LRCLK_FRC_MASK			0x0002
#define MADERA_AIF1RX_LRCLK_FRC_SHIFT			     1
#define MADERA_AIF1RX_LRCLK_FRC_WIDTH			     1
#define MADERA_AIF1RX_LRCLK_MSTR			0x0001
#define MADERA_AIF1RX_LRCLK_MSTR_MASK			0x0001
#define MADERA_AIF1RX_LRCLK_MSTR_SHIFT			     0
#define MADERA_AIF1RX_LRCLK_MSTR_WIDTH			     1

/* (0x0503)  AIF1_Rate_Ctrl */
#define MADERA_AIF1_RATE_MASK				0xF800
#define MADERA_AIF1_RATE_SHIFT				    11
#define MADERA_AIF1_RATE_WIDTH				     5
#define MADERA_AIF1_TRI					0x0040
#define MADERA_AIF1_TRI_MASK				0x0040
#define MADERA_AIF1_TRI_SHIFT				     6
#define MADERA_AIF1_TRI_WIDTH				     1

/* (0x0504)  AIF1_Format */
#define MADERA_AIF1_FMT_MASK				0x0007
#define MADERA_AIF1_FMT_SHIFT				     0
#define MADERA_AIF1_FMT_WIDTH				     3

/* (0x0506)  AIF1_Rx_BCLK_Rate */
#define MADERA_AIF1RX_BCPF_MASK				0x1FFF
#define MADERA_AIF1RX_BCPF_SHIFT			     0
#define MADERA_AIF1RX_BCPF_WIDTH			    13

/* (0x0507)  AIF1_Frame_Ctrl_1 */
#define MADERA_AIF1TX_WL_MASK				0x3F00
#define MADERA_AIF1TX_WL_SHIFT				     8
#define MADERA_AIF1TX_WL_WIDTH				     6
#define MADERA_AIF1TX_SLOT_LEN_MASK			0x00FF
#define MADERA_AIF1TX_SLOT_LEN_SHIFT			     0
#define MADERA_AIF1TX_SLOT_LEN_WIDTH			     8

/* (0x0508)  AIF1_Frame_Ctrl_2 */
#define MADERA_AIF1RX_WL_MASK				0x3F00
#define MADERA_AIF1RX_WL_SHIFT				     8
#define MADERA_AIF1RX_WL_WIDTH				     6
#define MADERA_AIF1RX_SLOT_LEN_MASK			0x00FF
#define MADERA_AIF1RX_SLOT_LEN_SHIFT			     0
#define MADERA_AIF1RX_SLOT_LEN_WIDTH			     8

/* (0x0509)  AIF1_Frame_Ctrl_3 */
#define MADERA_AIF1TX1_SLOT_MASK			0x003F
#define MADERA_AIF1TX1_SLOT_SHIFT			     0
#define MADERA_AIF1TX1_SLOT_WIDTH			     6

/* (0x0519)  AIF1_Tx_Enables */
#define MADERA_AIF1TX8_ENA				0x0080
#define MADERA_AIF1TX8_ENA_MASK				0x0080
#define MADERA_AIF1TX8_ENA_SHIFT			     7
#define MADERA_AIF1TX8_ENA_WIDTH			     1
#define MADERA_AIF1TX7_ENA				0x0040
#define MADERA_AIF1TX7_ENA_MASK				0x0040
#define MADERA_AIF1TX7_ENA_SHIFT			     6
#define MADERA_AIF1TX7_ENA_WIDTH			     1
#define MADERA_AIF1TX6_ENA				0x0020
#define MADERA_AIF1TX6_ENA_MASK				0x0020
#define MADERA_AIF1TX6_ENA_SHIFT			     5
#define MADERA_AIF1TX6_ENA_WIDTH			     1
#define MADERA_AIF1TX5_ENA				0x0010
#define MADERA_AIF1TX5_ENA_MASK				0x0010
#define MADERA_AIF1TX5_ENA_SHIFT			     4
#define MADERA_AIF1TX5_ENA_WIDTH			     1
#define MADERA_AIF1TX4_ENA				0x0008
#define MADERA_AIF1TX4_ENA_MASK				0x0008
#define MADERA_AIF1TX4_ENA_SHIFT			     3
#define MADERA_AIF1TX4_ENA_WIDTH			     1
#define MADERA_AIF1TX3_ENA				0x0004
#define MADERA_AIF1TX3_ENA_MASK				0x0004
#define MADERA_AIF1TX3_ENA_SHIFT			     2
#define MADERA_AIF1TX3_ENA_WIDTH			     1
#define MADERA_AIF1TX2_ENA				0x0002
#define MADERA_AIF1TX2_ENA_MASK				0x0002
#define MADERA_AIF1TX2_ENA_SHIFT			     1
#define MADERA_AIF1TX2_ENA_WIDTH			     1
#define MADERA_AIF1TX1_ENA				0x0001
#define MADERA_AIF1TX1_ENA_MASK				0x0001
#define MADERA_AIF1TX1_ENA_SHIFT			     0
#define MADERA_AIF1TX1_ENA_WIDTH			     1

/* (0x051A)  AIF1_Rx_Enables */
#define MADERA_AIF1RX8_ENA				0x0080
#define MADERA_AIF1RX8_ENA_MASK				0x0080
#define MADERA_AIF1RX8_ENA_SHIFT			     7
#define MADERA_AIF1RX8_ENA_WIDTH			     1
#define MADERA_AIF1RX7_ENA				0x0040
#define MADERA_AIF1RX7_ENA_MASK				0x0040
#define MADERA_AIF1RX7_ENA_SHIFT			     6
#define MADERA_AIF1RX7_ENA_WIDTH			     1
#define MADERA_AIF1RX6_ENA				0x0020
#define MADERA_AIF1RX6_ENA_MASK				0x0020
#define MADERA_AIF1RX6_ENA_SHIFT			     5
#define MADERA_AIF1RX6_ENA_WIDTH			     1
#define MADERA_AIF1RX5_ENA				0x0010
#define MADERA_AIF1RX5_ENA_MASK				0x0010
#define MADERA_AIF1RX5_ENA_SHIFT			     4
#define MADERA_AIF1RX5_ENA_WIDTH			     1
#define MADERA_AIF1RX4_ENA				0x0008
#define MADERA_AIF1RX4_ENA_MASK				0x0008
#define MADERA_AIF1RX4_ENA_SHIFT			     3
#define MADERA_AIF1RX4_ENA_WIDTH			     1
#define MADERA_AIF1RX3_ENA				0x0004
#define MADERA_AIF1RX3_ENA_MASK				0x0004
#define MADERA_AIF1RX3_ENA_SHIFT			     2
#define MADERA_AIF1RX3_ENA_WIDTH			     1
#define MADERA_AIF1RX2_ENA				0x0002
#define MADERA_AIF1RX2_ENA_MASK				0x0002
#define MADERA_AIF1RX2_ENA_SHIFT			     1
#define MADERA_AIF1RX2_ENA_WIDTH			     1
#define MADERA_AIF1RX1_ENA				0x0001
#define MADERA_AIF1RX1_ENA_MASK				0x0001
#define MADERA_AIF1RX1_ENA_SHIFT			     0
#define MADERA_AIF1RX1_ENA_WIDTH			     1

/* (0x0559)  AIF2_Tx_Enables */
#define MADERA_AIF2TX8_ENA				0x0080
#define MADERA_AIF2TX8_ENA_MASK				0x0080
#define MADERA_AIF2TX8_ENA_SHIFT			     7
#define MADERA_AIF2TX8_ENA_WIDTH			     1
#define MADERA_AIF2TX7_ENA				0x0040
#define MADERA_AIF2TX7_ENA_MASK				0x0040
#define MADERA_AIF2TX7_ENA_SHIFT			     6
#define MADERA_AIF2TX7_ENA_WIDTH			     1
#define MADERA_AIF2TX6_ENA				0x0020
#define MADERA_AIF2TX6_ENA_MASK				0x0020
#define MADERA_AIF2TX6_ENA_SHIFT			     5
#define MADERA_AIF2TX6_ENA_WIDTH			     1
#define MADERA_AIF2TX5_ENA				0x0010
#define MADERA_AIF2TX5_ENA_MASK				0x0010
#define MADERA_AIF2TX5_ENA_SHIFT			     4
#define MADERA_AIF2TX5_ENA_WIDTH			     1
#define MADERA_AIF2TX4_ENA				0x0008
#define MADERA_AIF2TX4_ENA_MASK				0x0008
#define MADERA_AIF2TX4_ENA_SHIFT			     3
#define MADERA_AIF2TX4_ENA_WIDTH			     1
#define MADERA_AIF2TX3_ENA				0x0004
#define MADERA_AIF2TX3_ENA_MASK				0x0004
#define MADERA_AIF2TX3_ENA_SHIFT			     2
#define MADERA_AIF2TX3_ENA_WIDTH			     1
#define MADERA_AIF2TX2_ENA				0x0002
#define MADERA_AIF2TX2_ENA_MASK				0x0002
#define MADERA_AIF2TX2_ENA_SHIFT			     1
#define MADERA_AIF2TX2_ENA_WIDTH			     1
#define MADERA_AIF2TX1_ENA				0x0001
#define MADERA_AIF2TX1_ENA_MASK				0x0001
#define MADERA_AIF2TX1_ENA_SHIFT			     0
#define MADERA_AIF2TX1_ENA_WIDTH			     1

/* (0x055A)  AIF2_Rx_Enables */
#define MADERA_AIF2RX8_ENA				0x0080
#define MADERA_AIF2RX8_ENA_MASK				0x0080
#define MADERA_AIF2RX8_ENA_SHIFT			     7
#define MADERA_AIF2RX8_ENA_WIDTH			     1
#define MADERA_AIF2RX7_ENA				0x0040
#define MADERA_AIF2RX7_ENA_MASK				0x0040
#define MADERA_AIF2RX7_ENA_SHIFT			     6
#define MADERA_AIF2RX7_ENA_WIDTH			     1
#define MADERA_AIF2RX6_ENA				0x0020
#define MADERA_AIF2RX6_ENA_MASK				0x0020
#define MADERA_AIF2RX6_ENA_SHIFT			     5
#define MADERA_AIF2RX6_ENA_WIDTH			     1
#define MADERA_AIF2RX5_ENA				0x0010
#define MADERA_AIF2RX5_ENA_MASK				0x0010
#define MADERA_AIF2RX5_ENA_SHIFT			     4
#define MADERA_AIF2RX5_ENA_WIDTH			     1
#define MADERA_AIF2RX4_ENA				0x0008
#define MADERA_AIF2RX4_ENA_MASK				0x0008
#define MADERA_AIF2RX4_ENA_SHIFT			     3
#define MADERA_AIF2RX4_ENA_WIDTH			     1
#define MADERA_AIF2RX3_ENA				0x0004
#define MADERA_AIF2RX3_ENA_MASK				0x0004
#define MADERA_AIF2RX3_ENA_SHIFT			     2
#define MADERA_AIF2RX3_ENA_WIDTH			     1
#define MADERA_AIF2RX2_ENA				0x0002
#define MADERA_AIF2RX2_ENA_MASK				0x0002
#define MADERA_AIF2RX2_ENA_SHIFT			     1
#define MADERA_AIF2RX2_ENA_WIDTH			     1
#define MADERA_AIF2RX1_ENA				0x0001
#define MADERA_AIF2RX1_ENA_MASK				0x0001
#define MADERA_AIF2RX1_ENA_SHIFT			     0
#define MADERA_AIF2RX1_ENA_WIDTH			     1

/* (0x0599)  AIF3_Tx_Enables */
#define MADERA_AIF3TX8_ENA				0x0080
#define MADERA_AIF3TX8_ENA_MASK				0x0080
#define MADERA_AIF3TX8_ENA_SHIFT			     7
#define MADERA_AIF3TX8_ENA_WIDTH			     1
#define MADERA_AIF3TX7_ENA				0x0040
#define MADERA_AIF3TX7_ENA_MASK				0x0040
#define MADERA_AIF3TX7_ENA_SHIFT			     6
#define MADERA_AIF3TX7_ENA_WIDTH			     1
#define MADERA_AIF3TX6_ENA				0x0020
#define MADERA_AIF3TX6_ENA_MASK				0x0020
#define MADERA_AIF3TX6_ENA_SHIFT			     5
#define MADERA_AIF3TX6_ENA_WIDTH			     1
#define MADERA_AIF3TX5_ENA				0x0010
#define MADERA_AIF3TX5_ENA_MASK				0x0010
#define MADERA_AIF3TX5_ENA_SHIFT			     4
#define MADERA_AIF3TX5_ENA_WIDTH			     1
#define MADERA_AIF3TX4_ENA				0x0008
#define MADERA_AIF3TX4_ENA_MASK				0x0008
#define MADERA_AIF3TX4_ENA_SHIFT			     3
#define MADERA_AIF3TX4_ENA_WIDTH			     1
#define MADERA_AIF3TX3_ENA				0x0004
#define MADERA_AIF3TX3_ENA_MASK				0x0004
#define MADERA_AIF3TX3_ENA_SHIFT			     2
#define MADERA_AIF3TX3_ENA_WIDTH			     1
#define MADERA_AIF3TX2_ENA				0x0002
#define MADERA_AIF3TX2_ENA_MASK				0x0002
#define MADERA_AIF3TX2_ENA_SHIFT			     1
#define MADERA_AIF3TX2_ENA_WIDTH			     1
#define MADERA_AIF3TX1_ENA				0x0001
#define MADERA_AIF3TX1_ENA_MASK				0x0001
#define MADERA_AIF3TX1_ENA_SHIFT			     0
#define MADERA_AIF3TX1_ENA_WIDTH			     1

/* (0x059A)  AIF3_Rx_Enables */
#define MADERA_AIF3RX8_ENA				0x0080
#define MADERA_AIF3RX8_ENA_MASK				0x0080
#define MADERA_AIF3RX8_ENA_SHIFT			     7
#define MADERA_AIF3RX8_ENA_WIDTH			     1
#define MADERA_AIF3RX7_ENA				0x0040
#define MADERA_AIF3RX7_ENA_MASK				0x0040
#define MADERA_AIF3RX7_ENA_SHIFT			     6
#define MADERA_AIF3RX7_ENA_WIDTH			     1
#define MADERA_AIF3RX6_ENA				0x0020
#define MADERA_AIF3RX6_ENA_MASK				0x0020
#define MADERA_AIF3RX6_ENA_SHIFT			     5
#define MADERA_AIF3RX6_ENA_WIDTH			     1
#define MADERA_AIF3RX5_ENA				0x0010
#define MADERA_AIF3RX5_ENA_MASK				0x0010
#define MADERA_AIF3RX5_ENA_SHIFT			     4
#define MADERA_AIF3RX5_ENA_WIDTH			     1
#define MADERA_AIF3RX4_ENA				0x0008
#define MADERA_AIF3RX4_ENA_MASK				0x0008
#define MADERA_AIF3RX4_ENA_SHIFT			     3
#define MADERA_AIF3RX4_ENA_WIDTH			     1
#define MADERA_AIF3RX3_ENA				0x0004
#define MADERA_AIF3RX3_ENA_MASK				0x0004
#define MADERA_AIF3RX3_ENA_SHIFT			     2
#define MADERA_AIF3RX3_ENA_WIDTH			     1
#define MADERA_AIF3RX2_ENA				0x0002
#define MADERA_AIF3RX2_ENA_MASK				0x0002
#define MADERA_AIF3RX2_ENA_SHIFT			     1
#define MADERA_AIF3RX2_ENA_WIDTH			     1
#define MADERA_AIF3RX1_ENA				0x0001
#define MADERA_AIF3RX1_ENA_MASK				0x0001
#define MADERA_AIF3RX1_ENA_SHIFT			     0
#define MADERA_AIF3RX1_ENA_WIDTH			     1

/* (0x05B9)  AIF4_Tx_Enables */
#define MADERA_AIF4TX2_ENA				0x0002
#define MADERA_AIF4TX2_ENA_MASK				0x0002
#define MADERA_AIF4TX2_ENA_SHIFT			     1
#define MADERA_AIF4TX2_ENA_WIDTH			     1
#define MADERA_AIF4TX1_ENA				0x0001
#define MADERA_AIF4TX1_ENA_MASK				0x0001
#define MADERA_AIF4TX1_ENA_SHIFT			     0
#define MADERA_AIF4TX1_ENA_WIDTH			     1

/* (0x05BA)  AIF4_Rx_Enables */
#define MADERA_AIF4RX2_ENA				0x0002
#define MADERA_AIF4RX2_ENA_MASK				0x0002
#define MADERA_AIF4RX2_ENA_SHIFT			     1
#define MADERA_AIF4RX2_ENA_WIDTH			     1
#define MADERA_AIF4RX1_ENA				0x0001
#define MADERA_AIF4RX1_ENA_MASK				0x0001
#define MADERA_AIF4RX1_ENA_SHIFT			     0
#define MADERA_AIF4RX1_ENA_WIDTH			     1

/* (0x05C2)  SPD1_TX_Control */
#define MADERA_SPD1_VAL2				0x2000
#define MADERA_SPD1_VAL2_MASK				0x2000
#define MADERA_SPD1_VAL2_SHIFT				    13
#define MADERA_SPD1_VAL2_WIDTH				     1
#define MADERA_SPD1_VAL1				0x1000
#define MADERA_SPD1_VAL1_MASK				0x1000
#define MADERA_SPD1_VAL1_SHIFT				    12
#define MADERA_SPD1_VAL1_WIDTH				     1
#define MADERA_SPD1_RATE_MASK				0x00F0
#define MADERA_SPD1_RATE_SHIFT				     4
#define MADERA_SPD1_RATE_WIDTH				     4
#define MADERA_SPD1_ENA					0x0001
#define MADERA_SPD1_ENA_MASK				0x0001
#define MADERA_SPD1_ENA_SHIFT				     0
#define MADERA_SPD1_ENA_WIDTH				     1

/* (0x05F5)  SLIMbus_RX_Channel_Enable */
#define MADERA_SLIMRX8_ENA				0x0080
#define MADERA_SLIMRX8_ENA_MASK				0x0080
#define MADERA_SLIMRX8_ENA_SHIFT			     7
#define MADERA_SLIMRX8_ENA_WIDTH			     1
#define MADERA_SLIMRX7_ENA				0x0040
#define MADERA_SLIMRX7_ENA_MASK				0x0040
#define MADERA_SLIMRX7_ENA_SHIFT			     6
#define MADERA_SLIMRX7_ENA_WIDTH			     1
#define MADERA_SLIMRX6_ENA				0x0020
#define MADERA_SLIMRX6_ENA_MASK				0x0020
#define MADERA_SLIMRX6_ENA_SHIFT			     5
#define MADERA_SLIMRX6_ENA_WIDTH			     1
#define MADERA_SLIMRX5_ENA				0x0010
#define MADERA_SLIMRX5_ENA_MASK				0x0010
#define MADERA_SLIMRX5_ENA_SHIFT			     4
#define MADERA_SLIMRX5_ENA_WIDTH			     1
#define MADERA_SLIMRX4_ENA				0x0008
#define MADERA_SLIMRX4_ENA_MASK				0x0008
#define MADERA_SLIMRX4_ENA_SHIFT			     3
#define MADERA_SLIMRX4_ENA_WIDTH			     1
#define MADERA_SLIMRX3_ENA				0x0004
#define MADERA_SLIMRX3_ENA_MASK				0x0004
#define MADERA_SLIMRX3_ENA_SHIFT			     2
#define MADERA_SLIMRX3_ENA_WIDTH			     1
#define MADERA_SLIMRX2_ENA				0x0002
#define MADERA_SLIMRX2_ENA_MASK				0x0002
#define MADERA_SLIMRX2_ENA_SHIFT			     1
#define MADERA_SLIMRX2_ENA_WIDTH			     1
#define MADERA_SLIMRX1_ENA				0x0001
#define MADERA_SLIMRX1_ENA_MASK				0x0001
#define MADERA_SLIMRX1_ENA_SHIFT			     0
#define MADERA_SLIMRX1_ENA_WIDTH			     1

/* (0x05F6)  SLIMbus_TX_Channel_Enable */
#define MADERA_SLIMTX8_ENA				0x0080
#define MADERA_SLIMTX8_ENA_MASK				0x0080
#define MADERA_SLIMTX8_ENA_SHIFT			     7
#define MADERA_SLIMTX8_ENA_WIDTH			     1
#define MADERA_SLIMTX7_ENA				0x0040
#define MADERA_SLIMTX7_ENA_MASK				0x0040
#define MADERA_SLIMTX7_ENA_SHIFT			     6
#define MADERA_SLIMTX7_ENA_WIDTH			     1
#define MADERA_SLIMTX6_ENA				0x0020
#define MADERA_SLIMTX6_ENA_MASK				0x0020
#define MADERA_SLIMTX6_ENA_SHIFT			     5
#define MADERA_SLIMTX6_ENA_WIDTH			     1
#define MADERA_SLIMTX5_ENA				0x0010
#define MADERA_SLIMTX5_ENA_MASK				0x0010
#define MADERA_SLIMTX5_ENA_SHIFT			     4
#define MADERA_SLIMTX5_ENA_WIDTH			     1
#define MADERA_SLIMTX4_ENA				0x0008
#define MADERA_SLIMTX4_ENA_MASK				0x0008
#define MADERA_SLIMTX4_ENA_SHIFT			     3
#define MADERA_SLIMTX4_ENA_WIDTH			     1
#define MADERA_SLIMTX3_ENA				0x0004
#define MADERA_SLIMTX3_ENA_MASK				0x0004
#define MADERA_SLIMTX3_ENA_SHIFT			     2
#define MADERA_SLIMTX3_ENA_WIDTH			     1
#define MADERA_SLIMTX2_ENA				0x0002
#define MADERA_SLIMTX2_ENA_MASK				0x0002
#define MADERA_SLIMTX2_ENA_SHIFT			     1
#define MADERA_SLIMTX2_ENA_WIDTH			     1
#define MADERA_SLIMTX1_ENA				0x0001
#define MADERA_SLIMTX1_ENA_MASK				0x0001
#define MADERA_SLIMTX1_ENA_SHIFT			     0
#define MADERA_SLIMTX1_ENA_WIDTH			     1

/* (0x0E10)  EQ1_1 */
#define MADERA_EQ1_B1_GAIN_MASK				0xF800
#define MADERA_EQ1_B1_GAIN_SHIFT			    11
#define MADERA_EQ1_B1_GAIN_WIDTH			     5
#define MADERA_EQ1_B2_GAIN_MASK				0x07C0
#define MADERA_EQ1_B2_GAIN_SHIFT			     6
#define MADERA_EQ1_B2_GAIN_WIDTH			     5
#define MADERA_EQ1_B3_GAIN_MASK				0x003E
#define MADERA_EQ1_B3_GAIN_SHIFT			     1
#define MADERA_EQ1_B3_GAIN_WIDTH			     5
#define MADERA_EQ1_ENA					0x0001
#define MADERA_EQ1_ENA_MASK				0x0001
#define MADERA_EQ1_ENA_SHIFT				     0
#define MADERA_EQ1_ENA_WIDTH				     1

/* (0x0E11)  EQ1_2 */
#define MADERA_EQ1_B4_GAIN_MASK				0xF800
#define MADERA_EQ1_B4_GAIN_SHIFT			    11
#define MADERA_EQ1_B4_GAIN_WIDTH			     5
#define MADERA_EQ1_B5_GAIN_MASK				0x07C0
#define MADERA_EQ1_B5_GAIN_SHIFT			     6
#define MADERA_EQ1_B5_GAIN_WIDTH			     5
#define MADERA_EQ1_B1_MODE				0x0001
#define MADERA_EQ1_B1_MODE_MASK				0x0001
#define MADERA_EQ1_B1_MODE_SHIFT			     0
#define MADERA_EQ1_B1_MODE_WIDTH			     1

/* (0x0E26)  EQ2_1 */
#define MADERA_EQ2_B1_GAIN_MASK				0xF800
#define MADERA_EQ2_B1_GAIN_SHIFT			    11
#define MADERA_EQ2_B1_GAIN_WIDTH			     5
#define MADERA_EQ2_B2_GAIN_MASK				0x07C0
#define MADERA_EQ2_B2_GAIN_SHIFT			     6
#define MADERA_EQ2_B2_GAIN_WIDTH			     5
#define MADERA_EQ2_B3_GAIN_MASK				0x003E
#define MADERA_EQ2_B3_GAIN_SHIFT			     1
#define MADERA_EQ2_B3_GAIN_WIDTH			     5
#define MADERA_EQ2_ENA					0x0001
#define MADERA_EQ2_ENA_MASK				0x0001
#define MADERA_EQ2_ENA_SHIFT				     0
#define MADERA_EQ2_ENA_WIDTH				     1

/* (0x0E27)  EQ2_2 */
#define MADERA_EQ2_B4_GAIN_MASK				0xF800
#define MADERA_EQ2_B4_GAIN_SHIFT			    11
#define MADERA_EQ2_B4_GAIN_WIDTH			     5
#define MADERA_EQ2_B5_GAIN_MASK				0x07C0
#define MADERA_EQ2_B5_GAIN_SHIFT			     6
#define MADERA_EQ2_B5_GAIN_WIDTH			     5
#define MADERA_EQ2_B1_MODE				0x0001
#define MADERA_EQ2_B1_MODE_MASK				0x0001
#define MADERA_EQ2_B1_MODE_SHIFT			     0
#define MADERA_EQ2_B1_MODE_WIDTH			     1

/* (0x0E3C)  EQ3_1 */
#define MADERA_EQ3_B1_GAIN_MASK				0xF800
#define MADERA_EQ3_B1_GAIN_SHIFT			    11
#define MADERA_EQ3_B1_GAIN_WIDTH			     5
#define MADERA_EQ3_B2_GAIN_MASK				0x07C0
#define MADERA_EQ3_B2_GAIN_SHIFT			     6
#define MADERA_EQ3_B2_GAIN_WIDTH			     5
#define MADERA_EQ3_B3_GAIN_MASK				0x003E
#define MADERA_EQ3_B3_GAIN_SHIFT			     1
#define MADERA_EQ3_B3_GAIN_WIDTH			     5
#define MADERA_EQ3_ENA					0x0001
#define MADERA_EQ3_ENA_MASK				0x0001
#define MADERA_EQ3_ENA_SHIFT				     0
#define MADERA_EQ3_ENA_WIDTH				     1

/* (0x0E3D)  EQ3_2 */
#define MADERA_EQ3_B4_GAIN_MASK				0xF800
#define MADERA_EQ3_B4_GAIN_SHIFT			    11
#define MADERA_EQ3_B4_GAIN_WIDTH			     5
#define MADERA_EQ3_B5_GAIN_MASK				0x07C0
#define MADERA_EQ3_B5_GAIN_SHIFT			     6
#define MADERA_EQ3_B5_GAIN_WIDTH			     5
#define MADERA_EQ3_B1_MODE				0x0001
#define MADERA_EQ3_B1_MODE_MASK				0x0001
#define MADERA_EQ3_B1_MODE_SHIFT			     0
#define MADERA_EQ3_B1_MODE_WIDTH			     1

/* (0x0E52)  EQ4_1 */
#define MADERA_EQ4_B1_GAIN_MASK				0xF800
#define MADERA_EQ4_B1_GAIN_SHIFT			    11
#define MADERA_EQ4_B1_GAIN_WIDTH			     5
#define MADERA_EQ4_B2_GAIN_MASK				0x07C0
#define MADERA_EQ4_B2_GAIN_SHIFT			     6
#define MADERA_EQ4_B2_GAIN_WIDTH			     5
#define MADERA_EQ4_B3_GAIN_MASK				0x003E
#define MADERA_EQ4_B3_GAIN_SHIFT			     1
#define MADERA_EQ4_B3_GAIN_WIDTH			     5
#define MADERA_EQ4_ENA					0x0001
#define MADERA_EQ4_ENA_MASK				0x0001
#define MADERA_EQ4_ENA_SHIFT				     0
#define MADERA_EQ4_ENA_WIDTH				     1

/* (0x0E53)  EQ4_2 */
#define MADERA_EQ4_B4_GAIN_MASK				0xF800
#define MADERA_EQ4_B4_GAIN_SHIFT			    11
#define MADERA_EQ4_B4_GAIN_WIDTH			     5
#define MADERA_EQ4_B5_GAIN_MASK				0x07C0
#define MADERA_EQ4_B5_GAIN_SHIFT			     6
#define MADERA_EQ4_B5_GAIN_WIDTH			     5
#define MADERA_EQ4_B1_MODE				0x0001
#define MADERA_EQ4_B1_MODE_MASK				0x0001
#define MADERA_EQ4_B1_MODE_SHIFT			     0
#define MADERA_EQ4_B1_MODE_WIDTH			     1

/* (0x0E80)  DRC1_ctrl1 */
#define MADERA_DRC1L_ENA				0x0002
#define MADERA_DRC1L_ENA_MASK				0x0002
#define MADERA_DRC1L_ENA_SHIFT				     1
#define MADERA_DRC1L_ENA_WIDTH				     1
#define MADERA_DRC1R_ENA				0x0001
#define MADERA_DRC1R_ENA_MASK				0x0001
#define MADERA_DRC1R_ENA_SHIFT				     0
#define MADERA_DRC1R_ENA_WIDTH				     1

/* (0x0E88)  DRC2_ctrl1 */
#define MADERA_DRC2L_ENA				0x0002
#define MADERA_DRC2L_ENA_MASK				0x0002
#define MADERA_DRC2L_ENA_SHIFT				     1
#define MADERA_DRC2L_ENA_WIDTH				     1
#define MADERA_DRC2R_ENA				0x0001
#define MADERA_DRC2R_ENA_MASK				0x0001
#define MADERA_DRC2R_ENA_SHIFT				     0
#define MADERA_DRC2R_ENA_WIDTH				     1

/* (0x0EC0)  HPLPF1_1 */
#define MADERA_LHPF1_MODE				0x0002
#define MADERA_LHPF1_MODE_MASK				0x0002
#define MADERA_LHPF1_MODE_SHIFT				     1
#define MADERA_LHPF1_MODE_WIDTH				     1
#define MADERA_LHPF1_ENA				0x0001
#define MADERA_LHPF1_ENA_MASK				0x0001
#define MADERA_LHPF1_ENA_SHIFT				     0
#define MADERA_LHPF1_ENA_WIDTH				     1

/* (0x0EC1)  HPLPF1_2 */
#define MADERA_LHPF1_COEFF_MASK				0xFFFF
#define MADERA_LHPF1_COEFF_SHIFT			     0
#define MADERA_LHPF1_COEFF_WIDTH			    16

/* (0x0EC4)  HPLPF2_1 */
#define MADERA_LHPF2_MODE				0x0002
#define MADERA_LHPF2_MODE_MASK				0x0002
#define MADERA_LHPF2_MODE_SHIFT				     1
#define MADERA_LHPF2_MODE_WIDTH				     1
#define MADERA_LHPF2_ENA				0x0001
#define MADERA_LHPF2_ENA_MASK				0x0001
#define MADERA_LHPF2_ENA_SHIFT				     0
#define MADERA_LHPF2_ENA_WIDTH				     1

/* (0x0EC5)  HPLPF2_2 */
#define MADERA_LHPF2_COEFF_MASK				0xFFFF
#define MADERA_LHPF2_COEFF_SHIFT			     0
#define MADERA_LHPF2_COEFF_WIDTH			    16

/* (0x0EC8)  HPLPF3_1 */
#define MADERA_LHPF3_MODE				0x0002
#define MADERA_LHPF3_MODE_MASK				0x0002
#define MADERA_LHPF3_MODE_SHIFT				     1
#define MADERA_LHPF3_MODE_WIDTH				     1
#define MADERA_LHPF3_ENA				0x0001
#define MADERA_LHPF3_ENA_MASK				0x0001
#define MADERA_LHPF3_ENA_SHIFT				     0
#define MADERA_LHPF3_ENA_WIDTH				     1

/* (0x0EC9)  HPLPF3_2 */
#define MADERA_LHPF3_COEFF_MASK				0xFFFF
#define MADERA_LHPF3_COEFF_SHIFT			     0
#define MADERA_LHPF3_COEFF_WIDTH			    16

/* (0x0ECC)  HPLPF4_1 */
#define MADERA_LHPF4_MODE				0x0002
#define MADERA_LHPF4_MODE_MASK				0x0002
#define MADERA_LHPF4_MODE_SHIFT				     1
#define MADERA_LHPF4_MODE_WIDTH				     1
#define MADERA_LHPF4_ENA				0x0001
#define MADERA_LHPF4_ENA_MASK				0x0001
#define MADERA_LHPF4_ENA_SHIFT				     0
#define MADERA_LHPF4_ENA_WIDTH				     1

/* (0x0ECD)  HPLPF4_2 */
#define MADERA_LHPF4_COEFF_MASK				0xFFFF
#define MADERA_LHPF4_COEFF_SHIFT			     0
#define MADERA_LHPF4_COEFF_WIDTH			    16

/* (0x0ED0)  ASRC2_ENABLE */
#define MADERA_ASRC2_IN2L_ENA				0x0008
#define MADERA_ASRC2_IN2L_ENA_MASK			0x0008
#define MADERA_ASRC2_IN2L_ENA_SHIFT			     3
#define MADERA_ASRC2_IN2L_ENA_WIDTH			     1
#define MADERA_ASRC2_IN2R_ENA				0x0004
#define MADERA_ASRC2_IN2R_ENA_MASK			0x0004
#define MADERA_ASRC2_IN2R_ENA_SHIFT			     2
#define MADERA_ASRC2_IN2R_ENA_WIDTH			     1
#define MADERA_ASRC2_IN1L_ENA				0x0002
#define MADERA_ASRC2_IN1L_ENA_MASK			0x0002
#define MADERA_ASRC2_IN1L_ENA_SHIFT			     1
#define MADERA_ASRC2_IN1L_ENA_WIDTH			     1
#define MADERA_ASRC2_IN1R_ENA				0x0001
#define MADERA_ASRC2_IN1R_ENA_MASK			0x0001
#define MADERA_ASRC2_IN1R_ENA_SHIFT			     0
#define MADERA_ASRC2_IN1R_ENA_WIDTH			     1

/* (0x0ED2)  ASRC2_RATE1 */
#define MADERA_ASRC2_RATE1_MASK				0xF800
#define MADERA_ASRC2_RATE1_SHIFT			    11
#define MADERA_ASRC2_RATE1_WIDTH			     5

/* (0x0ED3)  ASRC2_RATE2 */
#define MADERA_ASRC2_RATE2_MASK				0xF800
#define MADERA_ASRC2_RATE2_SHIFT			    11
#define MADERA_ASRC2_RATE2_WIDTH			     5

/* (0x0EE0)  ASRC1_ENABLE */
#define MADERA_ASRC1_IN2L_ENA				0x0008
#define MADERA_ASRC1_IN2L_ENA_MASK			0x0008
#define MADERA_ASRC1_IN2L_ENA_SHIFT			     3
#define MADERA_ASRC1_IN2L_ENA_WIDTH			     1
#define MADERA_ASRC1_IN2R_ENA				0x0004
#define MADERA_ASRC1_IN2R_ENA_MASK			0x0004
#define MADERA_ASRC1_IN2R_ENA_SHIFT			     2
#define MADERA_ASRC1_IN2R_ENA_WIDTH			     1
#define MADERA_ASRC1_IN1L_ENA				0x0002
#define MADERA_ASRC1_IN1L_ENA_MASK			0x0002
#define MADERA_ASRC1_IN1L_ENA_SHIFT			     1
#define MADERA_ASRC1_IN1L_ENA_WIDTH			     1
#define MADERA_ASRC1_IN1R_ENA				0x0001
#define MADERA_ASRC1_IN1R_ENA_MASK			0x0001
#define MADERA_ASRC1_IN1R_ENA_SHIFT			     0
#define MADERA_ASRC1_IN1R_ENA_WIDTH			     1

/* (0x0EE2)  ASRC1_RATE1 */
#define MADERA_ASRC1_RATE1_MASK				0xF800
#define MADERA_ASRC1_RATE1_SHIFT			    11
#define MADERA_ASRC1_RATE1_WIDTH			     5

/* (0x0EE3)  ASRC1_RATE2 */
#define MADERA_ASRC1_RATE2_MASK				0xF800
#define MADERA_ASRC1_RATE2_SHIFT			    11
#define MADERA_ASRC1_RATE2_WIDTH			     5

/* (0x0EF0) - ISRC1 CTRL 1 */
#define MADERA_ISRC1_FSH_MASK				0xF800
#define MADERA_ISRC1_FSH_SHIFT				    11
#define MADERA_ISRC1_FSH_WIDTH				     5
#define MADERA_ISRC1_CLK_SEL_MASK			0x0700
#define MADERA_ISRC1_CLK_SEL_SHIFT			     8
#define MADERA_ISRC1_CLK_SEL_WIDTH			     3

/* (0x0EF1)  ISRC1_CTRL_2 */
#define MADERA_ISRC1_FSL_MASK				0xF800
#define MADERA_ISRC1_FSL_SHIFT				    11
#define MADERA_ISRC1_FSL_WIDTH				     5

/* (0x0EF2)  ISRC1_CTRL_3 */
#define MADERA_ISRC1_INT1_ENA				0x8000
#define MADERA_ISRC1_INT1_ENA_MASK			0x8000
#define MADERA_ISRC1_INT1_ENA_SHIFT			    15
#define MADERA_ISRC1_INT1_ENA_WIDTH			     1
#define MADERA_ISRC1_INT2_ENA				0x4000
#define MADERA_ISRC1_INT2_ENA_MASK			0x4000
#define MADERA_ISRC1_INT2_ENA_SHIFT			    14
#define MADERA_ISRC1_INT2_ENA_WIDTH			     1
#define MADERA_ISRC1_INT3_ENA				0x2000
#define MADERA_ISRC1_INT3_ENA_MASK			0x2000
#define MADERA_ISRC1_INT3_ENA_SHIFT			    13
#define MADERA_ISRC1_INT3_ENA_WIDTH			     1
#define MADERA_ISRC1_INT4_ENA				0x1000
#define MADERA_ISRC1_INT4_ENA_MASK			0x1000
#define MADERA_ISRC1_INT4_ENA_SHIFT			    12
#define MADERA_ISRC1_INT4_ENA_WIDTH			     1
#define MADERA_ISRC1_DEC1_ENA				0x0200
#define MADERA_ISRC1_DEC1_ENA_MASK			0x0200
#define MADERA_ISRC1_DEC1_ENA_SHIFT			     9
#define MADERA_ISRC1_DEC1_ENA_WIDTH			     1
#define MADERA_ISRC1_DEC2_ENA				0x0100
#define MADERA_ISRC1_DEC2_ENA_MASK			0x0100
#define MADERA_ISRC1_DEC2_ENA_SHIFT			     8
#define MADERA_ISRC1_DEC2_ENA_WIDTH			     1
#define MADERA_ISRC1_DEC3_ENA				0x0080
#define MADERA_ISRC1_DEC3_ENA_MASK			0x0080
#define MADERA_ISRC1_DEC3_ENA_SHIFT			     7
#define MADERA_ISRC1_DEC3_ENA_WIDTH			     1
#define MADERA_ISRC1_DEC4_ENA				0x0040
#define MADERA_ISRC1_DEC4_ENA_MASK			0x0040
#define MADERA_ISRC1_DEC4_ENA_SHIFT			     6
#define MADERA_ISRC1_DEC4_ENA_WIDTH			     1
#define MADERA_ISRC1_NOTCH_ENA				0x0001
#define MADERA_ISRC1_NOTCH_ENA_MASK			0x0001
#define MADERA_ISRC1_NOTCH_ENA_SHIFT			     0
#define MADERA_ISRC1_NOTCH_ENA_WIDTH			     1

/* (0x0EF3)  ISRC2_CTRL_1 */
#define MADERA_ISRC2_FSH_MASK				0xF800
#define MADERA_ISRC2_FSH_SHIFT				    11
#define MADERA_ISRC2_FSH_WIDTH				     5
#define MADERA_ISRC2_CLK_SEL_MASK			0x0700
#define MADERA_ISRC2_CLK_SEL_SHIFT			     8
#define MADERA_ISRC2_CLK_SEL_WIDTH			     3

/* (0x0EF4)  ISRC2_CTRL_2 */
#define MADERA_ISRC2_FSL_MASK				0xF800
#define MADERA_ISRC2_FSL_SHIFT				    11
#define MADERA_ISRC2_FSL_WIDTH				     5

/* (0x0EF5)  ISRC2_CTRL_3 */
#define MADERA_ISRC2_INT1_ENA				0x8000
#define MADERA_ISRC2_INT1_ENA_MASK			0x8000
#define MADERA_ISRC2_INT1_ENA_SHIFT			    15
#define MADERA_ISRC2_INT1_ENA_WIDTH			     1
#define MADERA_ISRC2_INT2_ENA				0x4000
#define MADERA_ISRC2_INT2_ENA_MASK			0x4000
#define MADERA_ISRC2_INT2_ENA_SHIFT			    14
#define MADERA_ISRC2_INT2_ENA_WIDTH			     1
#define MADERA_ISRC2_INT3_ENA				0x2000
#define MADERA_ISRC2_INT3_ENA_MASK			0x2000
#define MADERA_ISRC2_INT3_ENA_SHIFT			    13
#define MADERA_ISRC2_INT3_ENA_WIDTH			     1
#define MADERA_ISRC2_INT4_ENA				0x1000
#define MADERA_ISRC2_INT4_ENA_MASK			0x1000
#define MADERA_ISRC2_INT4_ENA_SHIFT			    12
#define MADERA_ISRC2_INT4_ENA_WIDTH			     1
#define MADERA_ISRC2_DEC1_ENA				0x0200
#define MADERA_ISRC2_DEC1_ENA_MASK			0x0200
#define MADERA_ISRC2_DEC1_ENA_SHIFT			     9
#define MADERA_ISRC2_DEC1_ENA_WIDTH			     1
#define MADERA_ISRC2_DEC2_ENA				0x0100
#define MADERA_ISRC2_DEC2_ENA_MASK			0x0100
#define MADERA_ISRC2_DEC2_ENA_SHIFT			     8
#define MADERA_ISRC2_DEC2_ENA_WIDTH			     1
#define MADERA_ISRC2_DEC3_ENA				0x0080
#define MADERA_ISRC2_DEC3_ENA_MASK			0x0080
#define MADERA_ISRC2_DEC3_ENA_SHIFT			     7
#define MADERA_ISRC2_DEC3_ENA_WIDTH			     1
#define MADERA_ISRC2_DEC4_ENA				0x0040
#define MADERA_ISRC2_DEC4_ENA_MASK			0x0040
#define MADERA_ISRC2_DEC4_ENA_SHIFT			     6
#define MADERA_ISRC2_DEC4_ENA_WIDTH			     1
#define MADERA_ISRC2_NOTCH_ENA				0x0001
#define MADERA_ISRC2_NOTCH_ENA_MASK			0x0001
#define MADERA_ISRC2_NOTCH_ENA_SHIFT			     0
#define MADERA_ISRC2_NOTCH_ENA_WIDTH			     1

/* (0x0EF6)  ISRC3_CTRL_1 */
#define MADERA_ISRC3_FSH_MASK				0xF800
#define MADERA_ISRC3_FSH_SHIFT				    11
#define MADERA_ISRC3_FSH_WIDTH				     5
#define MADERA_ISRC3_CLK_SEL_MASK			0x0700
#define MADERA_ISRC3_CLK_SEL_SHIFT			     8
#define MADERA_ISRC3_CLK_SEL_WIDTH			     3

/* (0x0EF7)  ISRC3_CTRL_2 */
#define MADERA_ISRC3_FSL_MASK				0xF800
#define MADERA_ISRC3_FSL_SHIFT				    11
#define MADERA_ISRC3_FSL_WIDTH				     5

/* (0x0EF8)  ISRC3_CTRL_3 */
#define MADERA_ISRC3_INT1_ENA				0x8000
#define MADERA_ISRC3_INT1_ENA_MASK			0x8000
#define MADERA_ISRC3_INT1_ENA_SHIFT			    15
#define MADERA_ISRC3_INT1_ENA_WIDTH			     1
#define MADERA_ISRC3_INT2_ENA				0x4000
#define MADERA_ISRC3_INT2_ENA_MASK			0x4000
#define MADERA_ISRC3_INT2_ENA_SHIFT			    14
#define MADERA_ISRC3_INT2_ENA_WIDTH			     1
#define MADERA_ISRC3_INT3_ENA				0x2000
#define MADERA_ISRC3_INT3_ENA_MASK			0x2000
#define MADERA_ISRC3_INT3_ENA_SHIFT			    13
#define MADERA_ISRC3_INT3_ENA_WIDTH			     1
#define MADERA_ISRC3_INT4_ENA				0x1000
#define MADERA_ISRC3_INT4_ENA_MASK			0x1000
#define MADERA_ISRC3_INT4_ENA_SHIFT			    12
#define MADERA_ISRC3_INT4_ENA_WIDTH			     1
#define MADERA_ISRC3_DEC1_ENA				0x0200
#define MADERA_ISRC3_DEC1_ENA_MASK			0x0200
#define MADERA_ISRC3_DEC1_ENA_SHIFT			     9
#define MADERA_ISRC3_DEC1_ENA_WIDTH			     1
#define MADERA_ISRC3_DEC2_ENA				0x0100
#define MADERA_ISRC3_DEC2_ENA_MASK			0x0100
#define MADERA_ISRC3_DEC2_ENA_SHIFT			     8
#define MADERA_ISRC3_DEC2_ENA_WIDTH			     1
#define MADERA_ISRC3_DEC3_ENA				0x0080
#define MADERA_ISRC3_DEC3_ENA_MASK			0x0080
#define MADERA_ISRC3_DEC3_ENA_SHIFT			     7
#define MADERA_ISRC3_DEC3_ENA_WIDTH			     1
#define MADERA_ISRC3_DEC4_ENA				0x0040
#define MADERA_ISRC3_DEC4_ENA_MASK			0x0040
#define MADERA_ISRC3_DEC4_ENA_SHIFT			     6
#define MADERA_ISRC3_DEC4_ENA_WIDTH			     1
#define MADERA_ISRC3_NOTCH_ENA				0x0001
#define MADERA_ISRC3_NOTCH_ENA_MASK			0x0001
#define MADERA_ISRC3_NOTCH_ENA_SHIFT			     0
#define MADERA_ISRC3_NOTCH_ENA_WIDTH			     1

/* (0x0EF9)  ISRC4_CTRL_1 */
#define MADERA_ISRC4_FSH_MASK				0xF800
#define MADERA_ISRC4_FSH_SHIFT				    11
#define MADERA_ISRC4_FSH_WIDTH				     5
#define MADERA_ISRC4_CLK_SEL_MASK			0x0700
#define MADERA_ISRC4_CLK_SEL_SHIFT			     8
#define MADERA_ISRC4_CLK_SEL_WIDTH			     3

/* (0x0EFA)  ISRC4_CTRL_2 */
#define MADERA_ISRC4_FSL_MASK				0xF800
#define MADERA_ISRC4_FSL_SHIFT				    11
#define MADERA_ISRC4_FSL_WIDTH				     5

/* (0x0EFB)  ISRC4_CTRL_3 */
#define MADERA_ISRC4_INT1_ENA				0x8000
#define MADERA_ISRC4_INT1_ENA_MASK			0x8000
#define MADERA_ISRC4_INT1_ENA_SHIFT			    15
#define MADERA_ISRC4_INT1_ENA_WIDTH			     1
#define MADERA_ISRC4_INT2_ENA				0x4000
#define MADERA_ISRC4_INT2_ENA_MASK			0x4000
#define MADERA_ISRC4_INT2_ENA_SHIFT			    14
#define MADERA_ISRC4_INT2_ENA_WIDTH			     1
#define MADERA_ISRC4_INT3_ENA				0x2000
#define MADERA_ISRC4_INT3_ENA_MASK			0x2000
#define MADERA_ISRC4_INT3_ENA_SHIFT			    13
#define MADERA_ISRC4_INT3_ENA_WIDTH			     1
#define MADERA_ISRC4_INT4_ENA				0x1000
#define MADERA_ISRC4_INT4_ENA_MASK			0x1000
#define MADERA_ISRC4_INT4_ENA_SHIFT			    12
#define MADERA_ISRC4_INT4_ENA_WIDTH			     1
#define MADERA_ISRC4_DEC1_ENA				0x0200
#define MADERA_ISRC4_DEC1_ENA_MASK			0x0200
#define MADERA_ISRC4_DEC1_ENA_SHIFT			     9
#define MADERA_ISRC4_DEC1_ENA_WIDTH			     1
#define MADERA_ISRC4_DEC2_ENA				0x0100
#define MADERA_ISRC4_DEC2_ENA_MASK			0x0100
#define MADERA_ISRC4_DEC2_ENA_SHIFT			     8
#define MADERA_ISRC4_DEC2_ENA_WIDTH			     1
#define MADERA_ISRC4_DEC3_ENA				0x0080
#define MADERA_ISRC4_DEC3_ENA_MASK			0x0080
#define MADERA_ISRC4_DEC3_ENA_SHIFT			     7
#define MADERA_ISRC4_DEC3_ENA_WIDTH			     1
#define MADERA_ISRC4_DEC4_ENA				0x0040
#define MADERA_ISRC4_DEC4_ENA_MASK			0x0040
#define MADERA_ISRC4_DEC4_ENA_SHIFT			     6
#define MADERA_ISRC4_DEC4_ENA_WIDTH			     1
#define MADERA_ISRC4_NOTCH_ENA				0x0001
#define MADERA_ISRC4_NOTCH_ENA_MASK			0x0001
#define MADERA_ISRC4_NOTCH_ENA_SHIFT			     0
#define MADERA_ISRC4_NOTCH_ENA_WIDTH			     1

/* (0x0F00)  Clock_Control */
#define MADERA_EXT_NG_SEL_CLR				0x0080
#define MADERA_EXT_NG_SEL_CLR_MASK			0x0080
#define MADERA_EXT_NG_SEL_CLR_SHIFT			     7
#define MADERA_EXT_NG_SEL_CLR_WIDTH			     1
#define MADERA_EXT_NG_SEL_SET				0x0040
#define MADERA_EXT_NG_SEL_SET_MASK			0x0040
#define MADERA_EXT_NG_SEL_SET_SHIFT			     6
#define MADERA_EXT_NG_SEL_SET_WIDTH			     1
#define MADERA_CLK_R_ENA_CLR				0x0020
#define MADERA_CLK_R_ENA_CLR_MASK			0x0020
#define MADERA_CLK_R_ENA_CLR_SHIFT			     5
#define MADERA_CLK_R_ENA_CLR_WIDTH			     1
#define MADERA_CLK_R_ENA_SET				0x0010
#define MADERA_CLK_R_ENA_SET_MASK			0x0010
#define MADERA_CLK_R_ENA_SET_SHIFT			     4
#define MADERA_CLK_R_ENA_SET_WIDTH			     1
#define MADERA_CLK_NG_ENA_CLR				0x0008
#define MADERA_CLK_NG_ENA_CLR_MASK			0x0008
#define MADERA_CLK_NG_ENA_CLR_SHIFT			     3
#define MADERA_CLK_NG_ENA_CLR_WIDTH			     1
#define MADERA_CLK_NG_ENA_SET				0x0004
#define MADERA_CLK_NG_ENA_SET_MASK			0x0004
#define MADERA_CLK_NG_ENA_SET_SHIFT			     2
#define MADERA_CLK_NG_ENA_SET_WIDTH			     1
#define MADERA_CLK_L_ENA_CLR				0x0002
#define MADERA_CLK_L_ENA_CLR_MASK			0x0002
#define MADERA_CLK_L_ENA_CLR_SHIFT			     1
#define MADERA_CLK_L_ENA_CLR_WIDTH			     1
#define MADERA_CLK_L_ENA_SET				0x0001
#define MADERA_CLK_L_ENA_SET_MASK			0x0001
#define MADERA_CLK_L_ENA_SET_SHIFT			     0
#define MADERA_CLK_L_ENA_SET_WIDTH			     1

/* (0x0F01)  ANC_SRC */
#define MADERA_IN_RXANCR_SEL_MASK			0x0070
#define MADERA_IN_RXANCR_SEL_SHIFT			     4
#define MADERA_IN_RXANCR_SEL_WIDTH			     3
#define MADERA_IN_RXANCL_SEL_MASK			0x0007
#define MADERA_IN_RXANCL_SEL_SHIFT			     0
#define MADERA_IN_RXANCL_SEL_WIDTH			     3

/* (0x0F17)  FCL_ADC_reformatter_control */
#define MADERA_FCL_MIC_MODE_SEL				0x000C
#define MADERA_FCL_MIC_MODE_SEL_SHIFT			     2
#define MADERA_FCL_MIC_MODE_SEL_WIDTH			     2

/* (0x0F73)  FCR_ADC_reformatter_control */
#define MADERA_FCR_MIC_MODE_SEL				0x000C
#define MADERA_FCR_MIC_MODE_SEL_SHIFT			     2
#define MADERA_FCR_MIC_MODE_SEL_WIDTH			     2

/* (0x10C0)  AUXPDM1_CTRL_0 */
#define MADERA_AUXPDM1_SRC_MASK				0x0F00
#define MADERA_AUXPDM1_SRC_SHIFT			     8
#define MADERA_AUXPDM1_SRC_WIDTH			     4
#define MADERA_AUXPDM1_TXEDGE_MASK			0x0010
#define MADERA_AUXPDM1_TXEDGE_SHIFT			     4
#define MADERA_AUXPDM1_TXEDGE_WIDTH			     1
#define MADERA_AUXPDM1_MSTR_MASK			0x0008
#define MADERA_AUXPDM1_MSTR_SHIFT			     3
#define MADERA_AUXPDM1_MSTR_WIDTH			     1
#define MADERA_AUXPDM1_ENABLE_MASK			0x0001
#define MADERA_AUXPDM1_ENABLE_SHIFT			     0
#define MADERA_AUXPDM1_ENABLE_WIDTH			     1

/* (0x10C1)  AUXPDM1_CTRL_1 */
#define MADERA_AUXPDM1_CLK_FREQ_MASK			0xC000
#define MADERA_AUXPDM1_CLK_FREQ_SHIFT			    14
#define MADERA_AUXPDM1_CLK_FREQ_WIDTH			     2

/* (0x1480)  DFC1_CTRL_W0 */
#define MADERA_DFC1_RATE_MASK				0x007C
#define MADERA_DFC1_RATE_SHIFT				     2
#define MADERA_DFC1_RATE_WIDTH				     5
#define MADERA_DFC1_DITH_ENA				0x0002
#define MADERA_DFC1_DITH_ENA_MASK			0x0002
#define MADERA_DFC1_DITH_ENA_SHIFT			     1
#define MADERA_DFC1_DITH_ENA_WIDTH			     1
#define MADERA_DFC1_ENA					0x0001
#define MADERA_DFC1_ENA_MASK				0x0001
#define MADERA_DFC1_ENA_SHIFT				     0
#define MADERA_DFC1_ENA_WIDTH				     1

/* (0x1482)  DFC1_RX_W0 */
#define MADERA_DFC1_RX_DATA_WIDTH_MASK			0x1F00
#define MADERA_DFC1_RX_DATA_WIDTH_SHIFT			     8
#define MADERA_DFC1_RX_DATA_WIDTH_WIDTH			     5

#define MADERA_DFC1_RX_DATA_TYPE_MASK			0x0007
#define MADERA_DFC1_RX_DATA_TYPE_SHIFT			     0
#define MADERA_DFC1_RX_DATA_TYPE_WIDTH			     3

/* (0x1484)  DFC1_TX_W0 */
#define MADERA_DFC1_TX_DATA_WIDTH_MASK			0x1F00
#define MADERA_DFC1_TX_DATA_WIDTH_SHIFT			     8
#define MADERA_DFC1_TX_DATA_WIDTH_WIDTH			     5

#define MADERA_DFC1_TX_DATA_TYPE_MASK			0x0007
#define MADERA_DFC1_TX_DATA_TYPE_SHIFT			     0
#define MADERA_DFC1_TX_DATA_TYPE_WIDTH			     3

/* (0x1600)  ADSP2_IRQ0 */
#define MADERA_DSP_IRQ2					0x0002
#define MADERA_DSP_IRQ1					0x0001

/* (0x1601)  ADSP2_IRQ1 */
#define MADERA_DSP_IRQ4					0x0002
#define MADERA_DSP_IRQ3					0x0001

/* (0x1602)  ADSP2_IRQ2 */
#define MADERA_DSP_IRQ6					0x0002
#define MADERA_DSP_IRQ5					0x0001

/* (0x1603)  ADSP2_IRQ3 */
#define MADERA_DSP_IRQ8					0x0002
#define MADERA_DSP_IRQ7					0x0001

/* (0x1604)  ADSP2_IRQ4 */
#define MADERA_DSP_IRQ10				0x0002
#define MADERA_DSP_IRQ9					0x0001

/* (0x1605)  ADSP2_IRQ5 */
#define MADERA_DSP_IRQ12				0x0002
#define MADERA_DSP_IRQ11				0x0001

/* (0x1606)  ADSP2_IRQ6 */
#define MADERA_DSP_IRQ14				0x0002
#define MADERA_DSP_IRQ13				0x0001

/* (0x1607)  ADSP2_IRQ7 */
#define MADERA_DSP_IRQ16				0x0002
#define MADERA_DSP_IRQ15				0x0001

/* (0x1700)  GPIO1_CTRL_1 */
#define MADERA_GP1_LVL					0x8000
#define MADERA_GP1_LVL_MASK				0x8000
#define MADERA_GP1_LVL_SHIFT				    15
#define MADERA_GP1_LVL_WIDTH				     1
#define MADERA_GP1_OP_CFG				0x4000
#define MADERA_GP1_OP_CFG_MASK				0x4000
#define MADERA_GP1_OP_CFG_SHIFT				    14
#define MADERA_GP1_OP_CFG_WIDTH				     1
#define MADERA_GP1_DB					0x2000
#define MADERA_GP1_DB_MASK				0x2000
#define MADERA_GP1_DB_SHIFT				    13
#define MADERA_GP1_DB_WIDTH				     1
#define MADERA_GP1_POL					0x1000
#define MADERA_GP1_POL_MASK				0x1000
#define MADERA_GP1_POL_SHIFT				    12
#define MADERA_GP1_POL_WIDTH				     1
#define MADERA_GP1_IP_CFG				0x0800
#define MADERA_GP1_IP_CFG_MASK				0x0800
#define MADERA_GP1_IP_CFG_SHIFT				    11
#define MADERA_GP1_IP_CFG_WIDTH				     1
#define MADERA_GP1_FN_MASK				0x03FF
#define MADERA_GP1_FN_SHIFT				     0
#define MADERA_GP1_FN_WIDTH				    10

/* (0x1701)  GPIO1_CTRL_2 */
#define MADERA_GP1_DIR					0x8000
#define MADERA_GP1_DIR_MASK				0x8000
#define MADERA_GP1_DIR_SHIFT				    15
#define MADERA_GP1_DIR_WIDTH				     1
#define MADERA_GP1_PU					0x4000
#define MADERA_GP1_PU_MASK				0x4000
#define MADERA_GP1_PU_SHIFT				    14
#define MADERA_GP1_PU_WIDTH				     1
#define MADERA_GP1_PD					0x2000
#define MADERA_GP1_PD_MASK				0x2000
#define MADERA_GP1_PD_SHIFT				    13
#define MADERA_GP1_PD_WIDTH				     1
#define MADERA_GP1_DRV_STR_MASK				0x1800
#define MADERA_GP1_DRV_STR_SHIFT			    11
#define MADERA_GP1_DRV_STR_WIDTH			     2

/* (0x1800)  IRQ1_Status_1 */
#define MADERA_CTRLIF_ERR_EINT1				0x1000
#define MADERA_CTRLIF_ERR_EINT1_MASK			0x1000
#define MADERA_CTRLIF_ERR_EINT1_SHIFT			    12
#define MADERA_CTRLIF_ERR_EINT1_WIDTH			     1
#define MADERA_SYSCLK_FAIL_EINT1			0x0200
#define MADERA_SYSCLK_FAIL_EINT1_MASK			0x0200
#define MADERA_SYSCLK_FAIL_EINT1_SHIFT			     9
#define MADERA_SYSCLK_FAIL_EINT1_WIDTH			     1
#define MADERA_CLOCK_DETECT_EINT1			0x0100
#define MADERA_CLOCK_DETECT_EINT1_MASK			0x0100
#define MADERA_CLOCK_DETECT_EINT1_SHIFT			     8
#define MADERA_CLOCK_DETECT_EINT1_WIDTH			     1
#define MADERA_BOOT_DONE_EINT1				0x0080
#define MADERA_BOOT_DONE_EINT1_MASK			0x0080
#define MADERA_BOOT_DONE_EINT1_SHIFT			     7
#define MADERA_BOOT_DONE_EINT1_WIDTH			     1

/* (0x1801)  IRQ1_Status_2 */
#define MADERA_FLLAO_LOCK_EINT1				0x0800
#define MADERA_FLLAO_LOCK_EINT1_MASK			0x0800
#define MADERA_FLLAO_LOCK_EINT1_SHIFT			    11
#define MADERA_FLLAO_LOCK_EINT1_WIDTH			     1
#define MADERA_FLL3_LOCK_EINT1				0x0400
#define MADERA_FLL3_LOCK_EINT1_MASK			0x0400
#define MADERA_FLL3_LOCK_EINT1_SHIFT			    10
#define MADERA_FLL3_LOCK_EINT1_WIDTH			     1
#define MADERA_FLL2_LOCK_EINT1				0x0200
#define MADERA_FLL2_LOCK_EINT1_MASK			0x0200
#define MADERA_FLL2_LOCK_EINT1_SHIFT			     9
#define MADERA_FLL2_LOCK_EINT1_WIDTH			     1
#define MADERA_FLL1_LOCK_EINT1				0x0100
#define MADERA_FLL1_LOCK_EINT1_MASK			0x0100
#define MADERA_FLL1_LOCK_EINT1_SHIFT			     8
#define MADERA_FLL1_LOCK_EINT1_WIDTH			     1

/* (0x1805)  IRQ1_Status_6 */
#define MADERA_MICDET2_EINT1				0x0200
#define MADERA_MICDET2_EINT1_MASK			0x0200
#define MADERA_MICDET2_EINT1_SHIFT			     9
#define MADERA_MICDET2_EINT1_WIDTH			     1
#define MADERA_MICDET1_EINT1				0x0100
#define MADERA_MICDET1_EINT1_MASK			0x0100
#define MADERA_MICDET1_EINT1_SHIFT			     8
#define MADERA_MICDET1_EINT1_WIDTH			     1
#define MADERA_HPDET_EINT1				0x0001
#define MADERA_HPDET_EINT1_MASK				0x0001
#define MADERA_HPDET_EINT1_SHIFT			     0
#define MADERA_HPDET_EINT1_WIDTH			     1

/* (0x1806)  IRQ1_Status_7 */
#define MADERA_MICD_CLAMP_FALL_EINT1			0x0020
#define MADERA_MICD_CLAMP_FALL_EINT1_MASK		0x0020
#define MADERA_MICD_CLAMP_FALL_EINT1_SHIFT		     5
#define MADERA_MICD_CLAMP_FALL_EINT1_WIDTH		     1
#define MADERA_MICD_CLAMP_RISE_EINT1			0x0010
#define MADERA_MICD_CLAMP_RISE_EINT1_MASK		0x0010
#define MADERA_MICD_CLAMP_RISE_EINT1_SHIFT		     4
#define MADERA_MICD_CLAMP_RISE_EINT1_WIDTH		     1
#define MADERA_JD2_FALL_EINT1				0x0008
#define MADERA_JD2_FALL_EINT1_MASK			0x0008
#define MADERA_JD2_FALL_EINT1_SHIFT			     3
#define MADERA_JD2_FALL_EINT1_WIDTH			     1
#define MADERA_JD2_RISE_EINT1				0x0004
#define MADERA_JD2_RISE_EINT1_MASK			0x0004
#define MADERA_JD2_RISE_EINT1_SHIFT			     2
#define MADERA_JD2_RISE_EINT1_WIDTH			     1
#define MADERA_JD1_FALL_EINT1				0x0002
#define MADERA_JD1_FALL_EINT1_MASK			0x0002
#define MADERA_JD1_FALL_EINT1_SHIFT			     1
#define MADERA_JD1_FALL_EINT1_WIDTH			     1
#define MADERA_JD1_RISE_EINT1				0x0001
#define MADERA_JD1_RISE_EINT1_MASK			0x0001
#define MADERA_JD1_RISE_EINT1_SHIFT			     0
#define MADERA_JD1_RISE_EINT1_WIDTH			     1

/* (0x1808)  IRQ1_Status_9 */
#define MADERA_ASRC2_IN2_LOCK_EINT1			0x0800
#define MADERA_ASRC2_IN2_LOCK_EINT1_MASK		0x0800
#define MADERA_ASRC2_IN2_LOCK_EINT1_SHIFT		    11
#define MADERA_ASRC2_IN2_LOCK_EINT1_WIDTH		     1
#define MADERA_ASRC2_IN1_LOCK_EINT1			0x0400
#define MADERA_ASRC2_IN1_LOCK_EINT1_MASK		0x0400
#define MADERA_ASRC2_IN1_LOCK_EINT1_SHIFT		    10
#define MADERA_ASRC2_IN1_LOCK_EINT1_WIDTH		     1
#define MADERA_ASRC1_IN2_LOCK_EINT1			0x0200
#define MADERA_ASRC1_IN2_LOCK_EINT1_MASK		0x0200
#define MADERA_ASRC1_IN2_LOCK_EINT1_SHIFT		     9
#define MADERA_ASRC1_IN2_LOCK_EINT1_WIDTH		     1
#define MADERA_ASRC1_IN1_LOCK_EINT1			0x0100
#define MADERA_ASRC1_IN1_LOCK_EINT1_MASK		0x0100
#define MADERA_ASRC1_IN1_LOCK_EINT1_SHIFT		     8
#define MADERA_ASRC1_IN1_LOCK_EINT1_WIDTH		     1
#define MADERA_DRC2_SIG_DET_EINT1			0x0002
#define MADERA_DRC2_SIG_DET_EINT1_MASK			0x0002
#define MADERA_DRC2_SIG_DET_EINT1_SHIFT			     1
#define MADERA_DRC2_SIG_DET_EINT1_WIDTH			     1
#define MADERA_DRC1_SIG_DET_EINT1			0x0001
#define MADERA_DRC1_SIG_DET_EINT1_MASK			0x0001
#define MADERA_DRC1_SIG_DET_EINT1_SHIFT			     0
#define MADERA_DRC1_SIG_DET_EINT1_WIDTH			     1

/* (0x180A)  IRQ1_Status_11 */
#define MADERA_DSP_IRQ16_EINT1				0x8000
#define MADERA_DSP_IRQ16_EINT1_MASK			0x8000
#define MADERA_DSP_IRQ16_EINT1_SHIFT			    15
#define MADERA_DSP_IRQ16_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ15_EINT1				0x4000
#define MADERA_DSP_IRQ15_EINT1_MASK			0x4000
#define MADERA_DSP_IRQ15_EINT1_SHIFT			    14
#define MADERA_DSP_IRQ15_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ14_EINT1				0x2000
#define MADERA_DSP_IRQ14_EINT1_MASK			0x2000
#define MADERA_DSP_IRQ14_EINT1_SHIFT			    13
#define MADERA_DSP_IRQ14_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ13_EINT1				0x1000
#define MADERA_DSP_IRQ13_EINT1_MASK			0x1000
#define MADERA_DSP_IRQ13_EINT1_SHIFT			    12
#define MADERA_DSP_IRQ13_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ12_EINT1				0x0800
#define MADERA_DSP_IRQ12_EINT1_MASK			0x0800
#define MADERA_DSP_IRQ12_EINT1_SHIFT			    11
#define MADERA_DSP_IRQ12_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ11_EINT1				0x0400
#define MADERA_DSP_IRQ11_EINT1_MASK			0x0400
#define MADERA_DSP_IRQ11_EINT1_SHIFT			    10
#define MADERA_DSP_IRQ11_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ10_EINT1				0x0200
#define MADERA_DSP_IRQ10_EINT1_MASK			0x0200
#define MADERA_DSP_IRQ10_EINT1_SHIFT			     9
#define MADERA_DSP_IRQ10_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ9_EINT1				0x0100
#define MADERA_DSP_IRQ9_EINT1_MASK			0x0100
#define MADERA_DSP_IRQ9_EINT1_SHIFT			     8
#define MADERA_DSP_IRQ9_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ8_EINT1				0x0080
#define MADERA_DSP_IRQ8_EINT1_MASK			0x0080
#define MADERA_DSP_IRQ8_EINT1_SHIFT			     7
#define MADERA_DSP_IRQ8_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ7_EINT1				0x0040
#define MADERA_DSP_IRQ7_EINT1_MASK			0x0040
#define MADERA_DSP_IRQ7_EINT1_SHIFT			     6
#define MADERA_DSP_IRQ7_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ6_EINT1				0x0020
#define MADERA_DSP_IRQ6_EINT1_MASK			0x0020
#define MADERA_DSP_IRQ6_EINT1_SHIFT			     5
#define MADERA_DSP_IRQ6_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ5_EINT1				0x0010
#define MADERA_DSP_IRQ5_EINT1_MASK			0x0010
#define MADERA_DSP_IRQ5_EINT1_SHIFT			     4
#define MADERA_DSP_IRQ5_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ4_EINT1				0x0008
#define MADERA_DSP_IRQ4_EINT1_MASK			0x0008
#define MADERA_DSP_IRQ4_EINT1_SHIFT			     3
#define MADERA_DSP_IRQ4_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ3_EINT1				0x0004
#define MADERA_DSP_IRQ3_EINT1_MASK			0x0004
#define MADERA_DSP_IRQ3_EINT1_SHIFT			     2
#define MADERA_DSP_IRQ3_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ2_EINT1				0x0002
#define MADERA_DSP_IRQ2_EINT1_MASK			0x0002
#define MADERA_DSP_IRQ2_EINT1_SHIFT			     1
#define MADERA_DSP_IRQ2_EINT1_WIDTH			     1
#define MADERA_DSP_IRQ1_EINT1				0x0001
#define MADERA_DSP_IRQ1_EINT1_MASK			0x0001
#define MADERA_DSP_IRQ1_EINT1_SHIFT			     0
#define MADERA_DSP_IRQ1_EINT1_WIDTH			     1

/* (0x180B)  IRQ1_Status_12 */
#define MADERA_SPKOUTR_SC_EINT1				0x0080
#define MADERA_SPKOUTR_SC_EINT1_MASK			0x0080
#define MADERA_SPKOUTR_SC_EINT1_SHIFT			     7
#define MADERA_SPKOUTR_SC_EINT1_WIDTH			     1
#define MADERA_SPKOUTL_SC_EINT1				0x0040
#define MADERA_SPKOUTL_SC_EINT1_MASK			0x0040
#define MADERA_SPKOUTL_SC_EINT1_SHIFT			     6
#define MADERA_SPKOUTL_SC_EINT1_WIDTH			     1
#define MADERA_HP3R_SC_EINT1				0x0020
#define MADERA_HP3R_SC_EINT1_MASK			0x0020
#define MADERA_HP3R_SC_EINT1_SHIFT			     5
#define MADERA_HP3R_SC_EINT1_WIDTH			     1
#define MADERA_HP3L_SC_EINT1				0x0010
#define MADERA_HP3L_SC_EINT1_MASK			0x0010
#define MADERA_HP3L_SC_EINT1_SHIFT			     4
#define MADERA_HP3L_SC_EINT1_WIDTH			     1
#define MADERA_HP2R_SC_EINT1				0x0008
#define MADERA_HP2R_SC_EINT1_MASK			0x0008
#define MADERA_HP2R_SC_EINT1_SHIFT			     3
#define MADERA_HP2R_SC_EINT1_WIDTH			     1
#define MADERA_HP2L_SC_EINT1				0x0004
#define MADERA_HP2L_SC_EINT1_MASK			0x0004
#define MADERA_HP2L_SC_EINT1_SHIFT			     2
#define MADERA_HP2L_SC_EINT1_WIDTH			     1
#define MADERA_HP1R_SC_EINT1				0x0002
#define MADERA_HP1R_SC_EINT1_MASK			0x0002
#define MADERA_HP1R_SC_EINT1_SHIFT			     1
#define MADERA_HP1R_SC_EINT1_WIDTH			     1
#define MADERA_HP1L_SC_EINT1				0x0001
#define MADERA_HP1L_SC_EINT1_MASK			0x0001
#define MADERA_HP1L_SC_EINT1_SHIFT			     0
#define MADERA_HP1L_SC_EINT1_WIDTH			     1

/* (0x180E)  IRQ1_Status_15 */
#define MADERA_SPK_OVERHEAT_WARN_EINT1			0x0004
#define MADERA_SPK_OVERHEAT_WARN_EINT1_MASK		0x0004
#define MADERA_SPK_OVERHEAT_WARN_EINT1_SHIFT		     2
#define MADERA_SPK_OVERHEAT_WARN_EINT1_WIDTH		     1
#define MADERA_SPK_OVERHEAT_EINT1			0x0002
#define MADERA_SPK_OVERHEAT_EINT1_MASK			0x0002
#define MADERA_SPK_OVERHEAT_EINT1_SHIFT			     1
#define MADERA_SPK_OVERHEAT_EINT1_WIDTH			     1
#define MADERA_SPK_SHUTDOWN_EINT1			0x0001
#define MADERA_SPK_SHUTDOWN_EINT1_MASK			0x0001
#define MADERA_SPK_SHUTDOWN_EINT1_SHIFT			     0
#define MADERA_SPK_SHUTDOWN_EINT1_WIDTH			     1

/* (0x1820) - IRQ1 Status 33 */
#define MADERA_DSP7_BUS_ERR_EINT1			0x0040
#define MADERA_DSP7_BUS_ERR_EINT1_MASK			0x0040
#define MADERA_DSP7_BUS_ERR_EINT1_SHIFT			     6
#define MADERA_DSP7_BUS_ERR_EINT1_WIDTH			     1
#define MADERA_DSP6_BUS_ERR_EINT1			0x0020
#define MADERA_DSP6_BUS_ERR_EINT1_MASK			0x0020
#define MADERA_DSP6_BUS_ERR_EINT1_SHIFT			     5
#define MADERA_DSP6_BUS_ERR_EINT1_WIDTH			     1
#define MADERA_DSP5_BUS_ERR_EINT1			0x0010
#define MADERA_DSP5_BUS_ERR_EINT1_MASK			0x0010
#define MADERA_DSP5_BUS_ERR_EINT1_SHIFT			     4
#define MADERA_DSP5_BUS_ERR_EINT1_WIDTH			     1
#define MADERA_DSP4_BUS_ERR_EINT1			0x0008
#define MADERA_DSP4_BUS_ERR_EINT1_MASK			0x0008
#define MADERA_DSP4_BUS_ERR_EINT1_SHIFT			     3
#define MADERA_DSP4_BUS_ERR_EINT1_WIDTH			     1
#define MADERA_DSP3_BUS_ERR_EINT1			0x0004
#define MADERA_DSP3_BUS_ERR_EINT1_MASK			0x0004
#define MADERA_DSP3_BUS_ERR_EINT1_SHIFT			     2
#define MADERA_DSP3_BUS_ERR_EINT1_WIDTH			     1
#define MADERA_DSP2_BUS_ERR_EINT1			0x0002
#define MADERA_DSP2_BUS_ERR_EINT1_MASK			0x0002
#define MADERA_DSP2_BUS_ERR_EINT1_SHIFT			     1
#define MADERA_DSP2_BUS_ERR_EINT1_WIDTH			     1
#define MADERA_DSP1_BUS_ERR_EINT1			0x0001
#define MADERA_DSP1_BUS_ERR_EINT1_MASK			0x0001
#define MADERA_DSP1_BUS_ERR_EINT1_SHIFT			     0
#define MADERA_DSP1_BUS_ERR_EINT1_WIDTH			     1

/* (0x1845)  IRQ1_Mask_6 */
#define MADERA_IM_MICDET2_EINT1				0x0200
#define MADERA_IM_MICDET2_EINT1_MASK			0x0200
#define MADERA_IM_MICDET2_EINT1_SHIFT			     9
#define MADERA_IM_MICDET2_EINT1_WIDTH			     1
#define MADERA_IM_MICDET1_EINT1				0x0100
#define MADERA_IM_MICDET1_EINT1_MASK			0x0100
#define MADERA_IM_MICDET1_EINT1_SHIFT			     8
#define MADERA_IM_MICDET1_EINT1_WIDTH			     1
#define MADERA_IM_HPDET_EINT1				0x0001
#define MADERA_IM_HPDET_EINT1_MASK			0x0001
#define MADERA_IM_HPDET_EINT1_SHIFT			     0
#define MADERA_IM_HPDET_EINT1_WIDTH			     1
/* (0x184E)  IRQ1_Mask_15 */
#define MADERA_IM_SPK_OVERHEAT_WARN_EINT1		0x0004
#define MADERA_IM_SPK_OVERHEAT_WARN_EINT1_MASK		0x0004
#define MADERA_IM_SPK_OVERHEAT_WARN_EINT1_SHIFT		     2
#define MADERA_IM_SPK_OVERHEAT_WARN_EINT1_WIDTH		     1
#define MADERA_IM_SPK_OVERHEAT_EINT1			0x0002
#define MADERA_IM_SPK_OVERHEAT_EINT1_MASK		0x0002
#define MADERA_IM_SPK_OVERHEAT_EINT1_SHIFT		     1
#define MADERA_IM_SPK_OVERHEAT_EINT1_WIDTH		     1
#define MADERA_IM_SPK_SHUTDOWN_EINT1			0x0001
#define MADERA_IM_SPK_SHUTDOWN_EINT1_MASK		0x0001
#define MADERA_IM_SPK_SHUTDOWN_EINT1_SHIFT		     0
#define MADERA_IM_SPK_SHUTDOWN_EINT1_WIDTH		     1

/* (0x1880) - IRQ1 Raw Status 1 */
#define MADERA_CTRLIF_ERR_STS1				0x1000
#define MADERA_CTRLIF_ERR_STS1_MASK			0x1000
#define MADERA_CTRLIF_ERR_STS1_SHIFT			    12
#define MADERA_CTRLIF_ERR_STS1_WIDTH			     1
#define MADERA_SYSCLK_FAIL_STS1				0x0200
#define MADERA_SYSCLK_FAIL_STS1_MASK			0x0200
#define MADERA_SYSCLK_FAIL_STS1_SHIFT			     9
#define MADERA_SYSCLK_FAIL_STS1_WIDTH			     1
#define MADERA_CLOCK_DETECT_STS1			0x0100
#define MADERA_CLOCK_DETECT_STS1_MASK			0x0100
#define MADERA_CLOCK_DETECT_STS1_SHIFT			     8
#define MADERA_CLOCK_DETECT_STS1_WIDTH			     1
#define MADERA_BOOT_DONE_STS1				0x0080
#define MADERA_BOOT_DONE_STS1_MASK			0x0080
#define MADERA_BOOT_DONE_STS1_SHIFT			     7
#define MADERA_BOOT_DONE_STS1_WIDTH			     1

/* (0x1881) - IRQ1 Raw Status 2 */
#define MADERA_FLL3_LOCK_STS1				0x0400
#define MADERA_FLL3_LOCK_STS1_MASK			0x0400
#define MADERA_FLL3_LOCK_STS1_SHIFT			    10
#define MADERA_FLL3_LOCK_STS1_WIDTH			     1
#define MADERA_FLL2_LOCK_STS1				0x0200
#define MADERA_FLL2_LOCK_STS1_MASK			0x0200
#define MADERA_FLL2_LOCK_STS1_SHIFT			     9
#define MADERA_FLL2_LOCK_STS1_WIDTH			     1
#define MADERA_FLL1_LOCK_STS1				0x0100
#define MADERA_FLL1_LOCK_STS1_MASK			0x0100
#define MADERA_FLL1_LOCK_STS1_SHIFT			     8
#define MADERA_FLL1_LOCK_STS1_WIDTH			     1

/* (0x1886) - IRQ1 Raw Status 7 */
#define MADERA_MICD_CLAMP_FALL_STS1			0x0020
#define MADERA_MICD_CLAMP_FALL_STS1_MASK		0x0020
#define MADERA_MICD_CLAMP_FALL_STS1_SHIFT		     5
#define MADERA_MICD_CLAMP_FALL_STS1_WIDTH		     1
#define MADERA_MICD_CLAMP_RISE_STS1			0x0010
#define MADERA_MICD_CLAMP_RISE_STS1_MASK		0x0010
#define MADERA_MICD_CLAMP_RISE_STS1_SHIFT		     4
#define MADERA_MICD_CLAMP_RISE_STS1_WIDTH		     1
#define MADERA_JD2_FALL_STS1				0x0008
#define MADERA_JD2_FALL_STS1_MASK			0x0008
#define MADERA_JD2_FALL_STS1_SHIFT			     3
#define MADERA_JD2_FALL_STS1_WIDTH			     1
#define MADERA_JD2_RISE_STS1				0x0004
#define MADERA_JD2_RISE_STS1_MASK			0x0004
#define MADERA_JD2_RISE_STS1_SHIFT			     2
#define MADERA_JD2_RISE_STS1_WIDTH			     1
#define MADERA_JD1_FALL_STS1				0x0002
#define MADERA_JD1_FALL_STS1_MASK			0x0002
#define MADERA_JD1_FALL_STS1_SHIFT			     1
#define MADERA_JD1_FALL_STS1_WIDTH			     1
#define MADERA_JD1_RISE_STS1				0x0001
#define MADERA_JD1_RISE_STS1_MASK			0x0001
#define MADERA_JD1_RISE_STS1_SHIFT			     0
#define MADERA_JD1_RISE_STS1_WIDTH			     1

/* (0x188E) - IRQ1 Raw Status 15 */
#define MADERA_SPK_OVERHEAT_WARN_STS1			0x0004
#define MADERA_SPK_OVERHEAT_WARN_STS1_MASK		0x0004
#define MADERA_SPK_OVERHEAT_WARN_STS1_SHIFT		     2
#define MADERA_SPK_OVERHEAT_WARN_STS1_WIDTH		     1
#define MADERA_SPK_OVERHEAT_STS1			0x0002
#define MADERA_SPK_OVERHEAT_STS1_MASK			0x0002
#define MADERA_SPK_OVERHEAT_STS1_SHIFT			     1
#define MADERA_SPK_OVERHEAT_STS1_WIDTH			     1
#define MADERA_SPK_SHUTDOWN_STS1			0x0001
#define MADERA_SPK_SHUTDOWN_STS1_MASK			0x0001
#define MADERA_SPK_SHUTDOWN_STS1_SHIFT			     0
#define MADERA_SPK_SHUTDOWN_STS1_WIDTH			     1

/* (0x1A06)  Interrupt_Debounce_7 */
#define MADERA_MICD_CLAMP_DB				0x0010
#define MADERA_MICD_CLAMP_DB_MASK			0x0010
#define MADERA_MICD_CLAMP_DB_SHIFT			     4
#define MADERA_MICD_CLAMP_DB_WIDTH			     1
#define MADERA_JD2_DB					0x0004
#define MADERA_JD2_DB_MASK				0x0004
#define MADERA_JD2_DB_SHIFT				     2
#define MADERA_JD2_DB_WIDTH				     1
#define MADERA_JD1_DB					0x0001
#define MADERA_JD1_DB_MASK				0x0001
#define MADERA_JD1_DB_SHIFT				     0
#define MADERA_JD1_DB_WIDTH				     1

/* (0x1A0E)  Interrupt_Debounce_15 */
#define MADERA_SPK_OVERHEAT_WARN_DB			0x0004
#define MADERA_SPK_OVERHEAT_WARN_DB_MASK		0x0004
#define MADERA_SPK_OVERHEAT_WARN_DB_SHIFT		     2
#define MADERA_SPK_OVERHEAT_WARN_DB_WIDTH		     1
#define MADERA_SPK_OVERHEAT_DB				0x0002
#define MADERA_SPK_OVERHEAT_DB_MASK			0x0002
#define MADERA_SPK_OVERHEAT_DB_SHIFT			     1
#define MADERA_SPK_OVERHEAT_DB_WIDTH			     1

/* (0x1A80)  IRQ1_CTRL */
#define MADERA_IM_IRQ1					0x0800
#define MADERA_IM_IRQ1_MASK				0x0800
#define MADERA_IM_IRQ1_SHIFT				    11
#define MADERA_IM_IRQ1_WIDTH				     1
#define MADERA_IRQ_POL					0x0400
#define MADERA_IRQ_POL_MASK				0x0400
#define MADERA_IRQ_POL_SHIFT				    10
#define MADERA_IRQ_POL_WIDTH				     1

/* (0x20004)  OTP_HPDET_Cal_1 */
#define MADERA_OTP_HPDET_CALIB_OFFSET_11	    0xFF000000
#define MADERA_OTP_HPDET_CALIB_OFFSET_11_MASK	    0xFF000000
#define MADERA_OTP_HPDET_CALIB_OFFSET_11_SHIFT		    24
#define MADERA_OTP_HPDET_CALIB_OFFSET_11_WIDTH		     8
#define MADERA_OTP_HPDET_CALIB_OFFSET_10	    0x00FF0000
#define MADERA_OTP_HPDET_CALIB_OFFSET_10_MASK	    0x00FF0000
#define MADERA_OTP_HPDET_CALIB_OFFSET_10_SHIFT		    16
#define MADERA_OTP_HPDET_CALIB_OFFSET_10_WIDTH		     8
#define MADERA_OTP_HPDET_CALIB_OFFSET_01	    0x0000FF00
#define MADERA_OTP_HPDET_CALIB_OFFSET_01_MASK	    0x0000FF00
#define MADERA_OTP_HPDET_CALIB_OFFSET_01_SHIFT		     8
#define MADERA_OTP_HPDET_CALIB_OFFSET_01_WIDTH		     8
#define MADERA_OTP_HPDET_CALIB_OFFSET_00	    0x000000FF
#define MADERA_OTP_HPDET_CALIB_OFFSET_00_MASK	    0x000000FF
#define MADERA_OTP_HPDET_CALIB_OFFSET_00_SHIFT		     0
#define MADERA_OTP_HPDET_CALIB_OFFSET_00_WIDTH		     8

/* (0x20006)  OTP_HPDET_Cal_2 */
#define MADERA_OTP_HPDET_GRADIENT_1X		    0x0000FF00
#define MADERA_OTP_HPDET_GRADIENT_1X_MASK	    0x0000FF00
#define MADERA_OTP_HPDET_GRADIENT_1X_SHIFT		     8
#define MADERA_OTP_HPDET_GRADIENT_1X_WIDTH		     8
#define MADERA_OTP_HPDET_GRADIENT_0X		    0x000000FF
#define MADERA_OTP_HPDET_GRADIENT_0X_MASK	    0x000000FF
#define MADERA_OTP_HPDET_GRADIENT_0X_SHIFT		     0
#define MADERA_OTP_HPDET_GRADIENT_0X_WIDTH		     8

#endif
