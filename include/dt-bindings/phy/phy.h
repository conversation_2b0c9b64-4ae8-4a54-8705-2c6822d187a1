/* SPDX-License-Identifier: GPL-2.0-only */
/*
 *
 * This header provides constants for the phy framework
 *
 * Copyright (C) 2014 STMicroelectronics
 * Author: <PERSON> <gab<PERSON>.fernand<PERSON>@st.com>
 */

#ifndef _DT_BINDINGS_PHY
#define _DT_BINDINGS_PHY

#define PHY_NONE		0
#define PHY_TYPE_SATA		1
#define PHY_TYPE_PCIE		2
#define PHY_TYPE_USB2		3
#define PHY_TYPE_USB3		4
#define PHY_TYPE_UFS		5
#define PHY_TYPE_DP		6
#define PHY_TYPE_XPCS		7
#define PHY_TYPE_SGMII		8
#define PHY_TYPE_QSGMII		9

#endif /* _DT_BINDINGS_PHY */
