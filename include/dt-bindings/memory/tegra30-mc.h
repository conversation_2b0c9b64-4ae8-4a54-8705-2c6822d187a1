/* SPDX-License-Identifier: GPL-2.0 */
#ifndef DT_BINDINGS_MEMORY_TEGRA30_MC_H
#define DT_BINDINGS_MEMORY_TEGRA30_MC_H

#define TEGRA_SWGROUP_PTC	0
#define TEGRA_SWGROUP_DC	1
#define TEGRA_SWGROUP_DCB	2
#define TEGRA_SWGROUP_EPP	3
#define TEGRA_SWGROUP_G2	4
#define TEGRA_SWGROUP_MPE	5
#define TEGRA_SWGROUP_VI	6
#define TEGRA_SWGROUP_AFI	7
#define TEGRA_SWGROUP_AVPC	8
#define TEGRA_SWGROUP_NV	9
#define TEGRA_SWGROUP_NV2	10
#define TEGRA_SWGROUP_HDA	11
#define TEGRA_SWGROUP_HC	12
#define TEGRA_SWGROUP_PPCS	13
#define TEGRA_SWGROUP_SATA	14
#define TEGRA_SWGROUP_VDE	15
#define TEGRA_SWGROUP_MPCORELP	16
#define TEGRA_SWGROUP_MPCORE	17
#define TEGRA_SWGROUP_ISP	18

#define TEGRA30_MC_RESET_AFI		0
#define TEGRA30_MC_RESET_AVPC		1
#define TEGRA30_MC_RESET_DC		2
#define TEGRA30_MC_RESET_DCB		3
#define TEGRA30_MC_RESET_EPP		4
#define TEGRA30_MC_RESET_2D		5
#define TEGRA30_MC_RESET_HC		6
#define TEGRA30_MC_RESET_HDA		7
#define TEGRA30_MC_RESET_ISP		8
#define TEGRA30_MC_RESET_MPCORE		9
#define TEGRA30_MC_RESET_MPCORELP	10
#define TEGRA30_MC_RESET_MPE		11
#define TEGRA30_MC_RESET_3D		12
#define TEGRA30_MC_RESET_3D2		13
#define TEGRA30_MC_RESET_PPCS		14
#define TEGRA30_MC_RESET_SATA		15
#define TEGRA30_MC_RESET_VDE		16
#define TEGRA30_MC_RESET_VI		17

#endif
