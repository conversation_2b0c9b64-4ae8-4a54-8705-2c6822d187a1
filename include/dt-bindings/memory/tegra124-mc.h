/* SPDX-License-Identifier: GPL-2.0 */
#ifndef DT_BINDINGS_MEMORY_TEGRA124_MC_H
#define DT_BINDINGS_MEMORY_TEGRA124_MC_H

#define TEGRA_SWGROUP_PTC	0
#define TEGRA_SWGROUP_DC	1
#define TEGRA_SWGROUP_DCB	2
#define TEGRA_SWGROUP_AFI	3
#define TEGRA_SWGROUP_AVPC	4
#define TEGRA_SWGROUP_HDA	5
#define TEGRA_SWGROUP_HC	6
#define TEGRA_SWGROUP_MSENC	7
#define TEGRA_SWGROUP_PPCS	8
#define TEGRA_SWGROUP_SATA	9
#define TEGRA_SWGROUP_VDE	10
#define TEGRA_SWGROUP_MPCORELP	11
#define TEGRA_SWGROUP_MPCORE	12
#define TEGRA_SWGROUP_ISP2	13
#define TEGRA_SWGROUP_XUSB_HOST	14
#define TEGRA_SWGROUP_XUSB_DEV	15
#define TEGRA_SWGROUP_ISP2B	16
#define TEGRA_SWGROUP_TSEC	17
#define TEGRA_SWGROUP_A9AVP	18
#define TEGRA_SWGROUP_GPU	19
#define TEGRA_SWGROUP_SDMMC1A	20
#define TEGRA_SWGROUP_SDMMC2A	21
#define TEGRA_SWGROUP_SDMMC3A	22
#define TEGRA_SWGROUP_SDMMC4A	23
#define TEGRA_SWGROUP_VIC	24
#define TEGRA_SWGROUP_VI	25

#define TEGRA124_MC_RESET_AFI		0
#define TEGRA124_MC_RESET_AVPC		1
#define TEGRA124_MC_RESET_DC		2
#define TEGRA124_MC_RESET_DCB		3
#define TEGRA124_MC_RESET_HC		4
#define TEGRA124_MC_RESET_HDA		5
#define TEGRA124_MC_RESET_ISP2		6
#define TEGRA124_MC_RESET_MPCORE	7
#define TEGRA124_MC_RESET_MPCORELP	8
#define TEGRA124_MC_RESET_MSENC		9
#define TEGRA124_MC_RESET_PPCS		10
#define TEGRA124_MC_RESET_SATA		11
#define TEGRA124_MC_RESET_VDE		12
#define TEGRA124_MC_RESET_VI		13
#define TEGRA124_MC_RESET_VIC		14
#define TEGRA124_MC_RESET_XUSB_HOST	15
#define TEGRA124_MC_RESET_XUSB_DEV	16
#define TEGRA124_MC_RESET_TSEC		17
#define TEGRA124_MC_RESET_SDMMC1	18
#define TEGRA124_MC_RESET_SDMMC2	19
#define TEGRA124_MC_RESET_SDMMC3	20
#define TEGRA124_MC_RESET_SDMMC4	21
#define TEGRA124_MC_RESET_ISP2B		22
#define TEGRA124_MC_RESET_GPU		23

#endif
