/* SPDX-License-Identifier: GPL-2.0 */
#ifndef DT_BINDINGS_MEMORY_TEGRA20_MC_H
#define DT_BINDINGS_MEMORY_TEGRA20_MC_H

#define TEGRA20_MC_RESET_AVPC		0
#define TEGRA20_MC_RESET_DC		1
#define TEGRA20_MC_RESET_DCB		2
#define TEGRA20_MC_RESET_EPP		3
#define TEGRA20_MC_RESET_2D		4
#define TEGRA20_MC_RESET_HC		5
#define TEGRA20_MC_RESET_ISP		6
#define TEGRA20_MC_RESET_MPCORE		7
#define TEGRA20_MC_RESET_MPEA		8
#define TEGRA20_MC_RESET_MPEB		9
#define TEGRA20_MC_RESET_MPEC		10
#define TEGRA20_MC_RESET_3D		11
#define TEGRA20_MC_RESET_PPCS		12
#define TEGRA20_MC_RESET_VDE		13
#define TEGRA20_MC_RESET_VI		14

#endif
