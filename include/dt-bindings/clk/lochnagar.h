/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Devi<PERSON> Tree defines for Lochnagar clocking
 *
 * Copyright (c) 2017-2018 Cirrus Logic, Inc. and
 *                         Cirrus Logic International Semiconductor Ltd.
 *
 * Author: <PERSON> <<EMAIL>>
 */

#ifndef DT_BINDINGS_CLK_LOCHNAGAR_H
#define DT_BINDINGS_CLK_LOCHNAGAR_H

#define LOCHNAGAR_CDC_MCLK1		0
#define LOCHNAGAR_CDC_MCLK2		1
#define LOCHNAGAR_DSP_CLKIN		2
#define LOCHNAGAR_GF_CLKOUT1		3
#define LOCHNAGAR_GF_CLKOUT2		4
#define LOCHNAGAR_PSIA1_MCLK		5
#define LOCHNAGAR_PSIA2_MCLK		6
#define LOCHNAGAR_SPDIF_MCLK		7
#define LOCHNAGAR_ADAT_MCLK		8
#define LOCHNAGAR_SOUNDCARD_MCLK	9
#define LOCHNAGAR_SPDIF_CLKOUT		10

#endif
