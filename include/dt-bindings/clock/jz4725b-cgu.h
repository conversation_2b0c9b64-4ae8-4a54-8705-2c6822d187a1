/* SPDX-License-Identifier: GPL-2.0 */
/*
 * This header provides clock numbers for the ingenic,jz4725b-cgu DT binding.
 */

#ifndef __DT_BINDINGS_CLOCK_JZ4725B_CGU_H__
#define __DT_BINDINGS_CLOCK_JZ4725B_CGU_H__

#define JZ4725B_CLK_EXT		0
#define JZ4725B_CLK_OSC32K	1
#define JZ4725B_CLK_PLL		2
#define JZ4725B_CLK_PLL_HALF	3
#define JZ4725B_CLK_CCLK	4
#define JZ4725B_CLK_HCLK	5
#define JZ4725B_CLK_PCLK	6
#define JZ4725B_CLK_MCLK	7
#define JZ4725B_CLK_IPU		8
#define JZ4725B_CLK_LCD		9
#define JZ4725B_CLK_I2S		10
#define JZ4725B_CLK_SPI		11
#define JZ4725B_CLK_MMC_MUX	12
#define JZ4725B_CLK_UDC		13
#define JZ4725B_CLK_UART	14
#define JZ4725B_CLK_DMA		15
#define JZ4725B_CLK_ADC		16
#define JZ4725B_CLK_I2C		17
#define JZ4725B_CLK_AIC		18
#define JZ4725B_CLK_MMC0	19
#define JZ4725B_CLK_MMC1	20
#define JZ4725B_CLK_BCH		21
#define JZ4725B_CLK_TCU		22
#define JZ4725B_CLK_EXT512	23
#define JZ4725B_CLK_RTC		24
#define JZ4725B_CLK_UDC_PHY	25

#endif /* __DT_BINDINGS_CLOCK_JZ4725B_CGU_H__ */
