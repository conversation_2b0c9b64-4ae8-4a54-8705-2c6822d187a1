/* SPDX-License-Identifier: GPL-2.0 */
#ifndef __DT_BINDINGS_CLOCK_EFM32_CMU_H
#define __DT_BINDINGS_CLOCK_EFM32_CMU_H

#define clk_HFXO		0
#define clk_HFRCO		1
#define clk_LFXO		2
#define clk_LFRCO		3
#define clk_ULFRCO		4
#define clk_AUXHFRCO		5
#define clk_HFCLKNODIV		6
#define clk_HFCLK		7
#define clk_HFPERCLK		8
#define clk_HFCORECLK		9
#define clk_LFACLK		10
#define clk_LFBCLK		11
#define clk_WDOGCLK		12
#define clk_HFCORECLKDMA	13
#define clk_HFCORECLKAES	14
#define clk_HFCORECLKUSBC	15
#define clk_HFCORECLKUSB	16
#define clk_HFCORECLKLE		17
#define clk_HFCORECLKEBI	18
#define clk_HFPERCLKUSART0	19
#define clk_HFPERCLKUSART1	20
#define clk_HFPERCLKUSART2	21
#define clk_HFPERCLKUART0	22
#define clk_HFPERCLKUART1	23
#define clk_HFPERCLKTIMER0	24
#define clk_HFPERCLKTIMER1	25
#define clk_HFPERCLKTIMER2	26
#define clk_HFPERCLKTIMER3	27
#define clk_HFPERCLKACMP0	28
#define clk_HFPERCLKACMP1	29
#define clk_HFPERCLKI2C0	30
#define clk_HFPERCLKI2C1	31
#define clk_HFPERCLKGPIO	32
#define clk_HFPERCLKVCMP	33
#define clk_HFPERCLKPRS		34
#define clk_HFPERCLKADC0	35
#define clk_HFPERCLKDAC0	36

#endif /* __DT_BINDINGS_CLOCK_EFM32_CMU_H */
