/* SPDX-License-Identifier: GPL-2.0-only */
/*
 * Copyright (c) 2016, The Linux Foundation. All rights reserved.
 */


#ifndef _DT_BINDINGS_CLK_MSM_GCC_8994_H
#define _DT_BINDINGS_CLK_MSM_GCC_8994_H

#define GPLL0_EARLY				0
#define GPLL0					1
#define GPLL4_EARLY				2
#define GPLL4					3
#define UFS_AXI_CLK_SRC				4
#define USB30_MASTER_CLK_SRC			5
#define BLSP1_QUP1_I2C_APPS_CLK_SRC		6
#define BLSP1_QUP1_SPI_APPS_CLK_SRC		7
#define BLSP1_QUP2_I2C_APPS_CLK_SRC		8
#define BLSP1_QUP2_SPI_APPS_CLK_SRC		9
#define BLSP1_QUP3_I2C_APPS_CLK_SRC		10
#define BLSP1_QUP3_SPI_APPS_CLK_SRC		11
#define BLSP1_QUP4_I2C_APPS_CLK_SRC		12
#define BLSP1_QUP4_SPI_APPS_CLK_SRC		13
#define BLSP1_QUP5_I2C_APPS_CLK_SRC		14
#define BLSP1_QUP5_SPI_APPS_CLK_SRC		15
#define BLSP1_QUP6_I2C_APPS_CLK_SRC		16
#define BLSP1_QUP6_SPI_APPS_CLK_SRC		17
#define BLSP1_UART1_APPS_CLK_SRC		18
#define BLSP1_UART2_APPS_CLK_SRC		19
#define BLSP1_UART3_APPS_CLK_SRC		20
#define BLSP1_UART4_APPS_CLK_SRC		21
#define BLSP1_UART5_APPS_CLK_SRC		22
#define BLSP1_UART6_APPS_CLK_SRC		23
#define BLSP2_QUP1_I2C_APPS_CLK_SRC		24
#define BLSP2_QUP1_SPI_APPS_CLK_SRC		25
#define BLSP2_QUP2_I2C_APPS_CLK_SRC		26
#define BLSP2_QUP2_SPI_APPS_CLK_SRC		27
#define BLSP2_QUP3_I2C_APPS_CLK_SRC		28
#define BLSP2_QUP3_SPI_APPS_CLK_SRC		29
#define BLSP2_QUP4_I2C_APPS_CLK_SRC		30
#define BLSP2_QUP4_SPI_APPS_CLK_SRC		31
#define BLSP2_QUP5_I2C_APPS_CLK_SRC		32
#define BLSP2_QUP5_SPI_APPS_CLK_SRC		33
#define BLSP2_QUP6_I2C_APPS_CLK_SRC		34
#define BLSP2_QUP6_SPI_APPS_CLK_SRC		35
#define BLSP2_UART1_APPS_CLK_SRC		36
#define BLSP2_UART2_APPS_CLK_SRC		37
#define BLSP2_UART3_APPS_CLK_SRC		38
#define BLSP2_UART4_APPS_CLK_SRC		39
#define BLSP2_UART5_APPS_CLK_SRC		40
#define BLSP2_UART6_APPS_CLK_SRC		41
#define GP1_CLK_SRC				42
#define GP2_CLK_SRC				43
#define GP3_CLK_SRC				44
#define PCIE_0_AUX_CLK_SRC			45
#define PCIE_0_PIPE_CLK_SRC			46
#define PCIE_1_AUX_CLK_SRC			47
#define PCIE_1_PIPE_CLK_SRC			48
#define PDM2_CLK_SRC				49
#define SDCC1_APPS_CLK_SRC			50
#define SDCC2_APPS_CLK_SRC			51
#define SDCC3_APPS_CLK_SRC			52
#define SDCC4_APPS_CLK_SRC			53
#define TSIF_REF_CLK_SRC			54
#define USB30_MOCK_UTMI_CLK_SRC			55
#define USB3_PHY_AUX_CLK_SRC			56
#define USB_HS_SYSTEM_CLK_SRC			57
#define GCC_BLSP1_AHB_CLK			58
#define GCC_BLSP1_QUP1_I2C_APPS_CLK		59
#define GCC_BLSP1_QUP1_SPI_APPS_CLK		60
#define GCC_BLSP1_QUP2_I2C_APPS_CLK		61
#define GCC_BLSP1_QUP2_SPI_APPS_CLK		62
#define GCC_BLSP1_QUP3_I2C_APPS_CLK		63
#define GCC_BLSP1_QUP3_SPI_APPS_CLK		64
#define GCC_BLSP1_QUP4_I2C_APPS_CLK		65
#define GCC_BLSP1_QUP4_SPI_APPS_CLK		66
#define GCC_BLSP1_QUP5_I2C_APPS_CLK		67
#define GCC_BLSP1_QUP5_SPI_APPS_CLK		68
#define GCC_BLSP1_QUP6_I2C_APPS_CLK		69
#define GCC_BLSP1_QUP6_SPI_APPS_CLK		70
#define GCC_BLSP1_UART1_APPS_CLK		71
#define GCC_BLSP1_UART2_APPS_CLK		72
#define GCC_BLSP1_UART3_APPS_CLK		73
#define GCC_BLSP1_UART4_APPS_CLK		74
#define GCC_BLSP1_UART5_APPS_CLK		75
#define GCC_BLSP1_UART6_APPS_CLK		76
#define GCC_BLSP2_AHB_CLK			77
#define GCC_BLSP2_QUP1_I2C_APPS_CLK		78
#define GCC_BLSP2_QUP1_SPI_APPS_CLK		79
#define GCC_BLSP2_QUP2_I2C_APPS_CLK		80
#define GCC_BLSP2_QUP2_SPI_APPS_CLK		81
#define GCC_BLSP2_QUP3_I2C_APPS_CLK		82
#define GCC_BLSP2_QUP3_SPI_APPS_CLK		83
#define GCC_BLSP2_QUP4_I2C_APPS_CLK		84
#define GCC_BLSP2_QUP4_SPI_APPS_CLK		85
#define GCC_BLSP2_QUP5_I2C_APPS_CLK		86
#define GCC_BLSP2_QUP5_SPI_APPS_CLK		87
#define GCC_BLSP2_QUP6_I2C_APPS_CLK		88
#define GCC_BLSP2_QUP6_SPI_APPS_CLK		89
#define GCC_BLSP2_UART1_APPS_CLK		90
#define GCC_BLSP2_UART2_APPS_CLK		91
#define GCC_BLSP2_UART3_APPS_CLK		92
#define GCC_BLSP2_UART4_APPS_CLK		93
#define GCC_BLSP2_UART5_APPS_CLK		94
#define GCC_BLSP2_UART6_APPS_CLK		95
#define GCC_GP1_CLK				96
#define GCC_GP2_CLK				97
#define GCC_GP3_CLK				98
#define GCC_PCIE_0_AUX_CLK			99
#define GCC_PCIE_0_PIPE_CLK			100
#define GCC_PCIE_1_AUX_CLK			101
#define GCC_PCIE_1_PIPE_CLK			102
#define GCC_PDM2_CLK				103
#define GCC_SDCC1_APPS_CLK			104
#define GCC_SDCC2_APPS_CLK			105
#define GCC_SDCC3_APPS_CLK			106
#define GCC_SDCC4_APPS_CLK			107
#define GCC_SYS_NOC_UFS_AXI_CLK			108
#define GCC_SYS_NOC_USB3_AXI_CLK		109
#define GCC_TSIF_REF_CLK			110
#define GCC_UFS_AXI_CLK				111
#define GCC_UFS_RX_CFG_CLK			112
#define GCC_UFS_TX_CFG_CLK			113
#define GCC_USB30_MASTER_CLK			114
#define GCC_USB30_MOCK_UTMI_CLK			115
#define GCC_USB3_PHY_AUX_CLK			116
#define GCC_USB_HS_SYSTEM_CLK			117
#define GCC_SDCC1_AHB_CLK			118
#define GCC_LPASS_Q6_AXI_CLK		119
#define GCC_MSS_Q6_BIMC_AXI_CLK		120
#define GCC_PCIE_0_CFG_AHB_CLK		121
#define GCC_PCIE_0_MSTR_AXI_CLK		122
#define GCC_PCIE_0_SLV_AXI_CLK		123
#define GCC_PCIE_1_CFG_AHB_CLK		124
#define GCC_PCIE_1_MSTR_AXI_CLK		125
#define GCC_PCIE_1_SLV_AXI_CLK		126
#define GCC_PDM_AHB_CLK				127
#define GCC_SDCC2_AHB_CLK			128
#define GCC_SDCC3_AHB_CLK			129
#define GCC_SDCC4_AHB_CLK			130
#define GCC_TSIF_AHB_CLK			131
#define GCC_UFS_AHB_CLK				132
#define GCC_UFS_RX_SYMBOL_0_CLK		133
#define GCC_UFS_RX_SYMBOL_1_CLK		134
#define GCC_UFS_TX_SYMBOL_0_CLK		135
#define GCC_UFS_TX_SYMBOL_1_CLK		136
#define GCC_USB2_HS_PHY_SLEEP_CLK	137
#define GCC_USB30_SLEEP_CLK			138
#define GCC_USB_HS_AHB_CLK			139
#define GCC_USB_PHY_CFG_AHB2PHY_CLK	140

/* GDSCs */
#define PCIE_GDSC			0
#define PCIE_0_GDSC			1
#define PCIE_1_GDSC			2
#define USB30_GDSC			3
#define UFS_GDSC			4

/* Resets */
#define USB3_PHY_RESET			0
#define USB3PHY_PHY_RESET		1
#define PCIE_PHY_0_RESET		2
#define PCIE_PHY_1_RESET		3
#define QUSB2_PHY_RESET			4

#endif
