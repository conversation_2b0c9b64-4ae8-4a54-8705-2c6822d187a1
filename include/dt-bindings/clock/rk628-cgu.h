/* SPDX-License-Identifier: (GPL-2.0+ OR MIT) */
/*
 * Copyright (c) 2020 Rockchip Electronics Co. Ltd.
 *
 * Author: Wyon Bi <<EMAIL>>
 */

#ifndef _RK628_CGU_H
#define _RK628_CGU_H

#define CGU_CLK_CPLL		1
#define CGU_CLK_GPLL		2
#define CGU_CLK_CPLL_MUX	3
#define CGU_CLK_GPLL_MUX	4
#define CGU_PCLK_GPIO0		5
#define CGU_PCLK_GPIO1		6
#define CGU_PCLK_GPIO2		7
#define CGU_PCLK_GPIO3		8
#define CGU_PCLK_TXPHY_CON	9
#define CGU_PCLK_EFUSE		10
#define CGU_PCLK_DSI0		11
#define CGU_PCLK_DSI1		12
#define CGU_PCLK_CSI		13
#define CGU_PCLK_HDMITX		14
#define CGU_PCLK_RXPHY		15
#define CGU_PCLK_HDMIRX		16
#define CGU_PCLK_DPRX		17
#define CGU_PCLK_GVIHOST	18
#define CGU_CLK_CFG_DPHY0	19
#define CGU_CLK_CFG_DPHY1	20
#define CGU_CLK_TXESC		21
#define CGU_CLK_DPRX_VID	22
#define CGU_CLK_IMODET		23
#define CGU_CLK_HDMIRX_AUD	24
#define CGU_CLK_HDMIRX_CEC	25
#define CGU_CLK_RX_READ		26
#define CGU_SCLK_VOP		27
#define CGU_PCLK_LOGIC		28
#define CGU_CLK_GPIO_DB0	29
#define CGU_CLK_GPIO_DB1	30
#define CGU_CLK_GPIO_DB2	31
#define CGU_CLK_GPIO_DB3	32
#define CGU_CLK_I2S_8CH_SRC	33
#define CGU_CLK_I2S_8CH_FRAC	34
#define CGU_MCLK_I2S_8CH	35
#define CGU_I2S_MCLKOUT		36
#define CGU_BT1120DEC		37
#define CGU_CLK_TESTOUT		38
#define CGU_NR_CLKS		39

#endif
