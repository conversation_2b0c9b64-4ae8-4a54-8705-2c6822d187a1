/* SPDX-License-Identifier: GPL-2.0 */
/* Copyright (c) 2018, 2020, The Linux Foundation. All rights reserved. */


#ifndef _DT_BINDINGS_CLK_MSM_RPMH_H
#define _DT_BINDINGS_CLK_MSM_RPMH_H

/* RPMh controlled clocks */
#define RPMH_CXO_CLK				0
#define RPMH_CXO_CLK_A				1
#define RPMH_LN_BB_CLK2				2
#define RPMH_LN_BB_CLK2_A			3
#define RPMH_LN_BB_CLK3				4
#define RPMH_LN_BB_CLK3_A			5
#define RPMH_RF_CLK1				6
#define RPMH_RF_CLK1_A				7
#define RPMH_RF_CLK2				8
#define RPMH_RF_CLK2_A				9
#define RPMH_RF_CLK3				10
#define RPMH_RF_CLK3_A				11
#define RPMH_IPA_CLK				12
#define RPMH_LN_BB_CLK1				13
#define RPMH_LN_BB_CLK1_A			14

#endif
