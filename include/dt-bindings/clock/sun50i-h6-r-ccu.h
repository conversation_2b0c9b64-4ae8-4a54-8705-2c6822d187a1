/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Copyright (c) 2017 Icenowy Zheng <<EMAIL>>
 */

#ifndef _DT_BINDINGS_CLK_SUN50I_H6_R_CCU_H_
#define _DT_BINDINGS_CLK_SUN50I_H6_R_CCU_H_

#define CLK_AR100		0

#define CLK_R_APB1		2

#define CLK_R_APB1_TIMER	4
#define CLK_R_APB1_TWD		5
#define CLK_R_APB1_PWM		6
#define CLK_R_APB2_UART		7
#define CLK_R_APB2_I2C		8
#define CLK_R_APB1_IR		9
#define CLK_R_APB1_W1		10

#define CLK_IR			11
#define CLK_W1			12

#endif /* _DT_BINDINGS_CLK_SUN50I_H6_R_CCU_H_ */
