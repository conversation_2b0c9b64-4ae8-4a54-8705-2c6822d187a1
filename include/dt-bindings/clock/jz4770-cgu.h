/* SPDX-License-Identifier: GPL-2.0 */
/*
 * This header provides clock numbers for the ingenic,jz4770-cgu DT binding.
 */

#ifndef __DT_BINDINGS_CLOCK_JZ4770_CGU_H__
#define __DT_BINDINGS_CLOCK_JZ4770_CGU_H__

#define JZ4770_CLK_EXT		0
#define JZ4770_CLK_OSC32K	1
#define JZ4770_CLK_PLL0		2
#define JZ4770_CLK_PLL1		3
#define JZ4770_CLK_CCLK		4
#define JZ4770_CLK_H0CLK	5
#define JZ4770_CLK_H1CLK	6
#define JZ4770_CLK_H2CLK	7
#define JZ4770_CLK_C1CLK	8
#define JZ4770_CLK_PCLK		9
#define JZ4770_CLK_MMC0_MUX	10
#define JZ4770_CLK_MMC0		11
#define JZ4770_CLK_MMC1_MUX	12
#define JZ4770_CLK_MMC1		13
#define JZ4770_CLK_MMC2_MUX	14
#define JZ4770_CLK_MMC2		15
#define JZ4770_CLK_CIM		16
#define JZ4770_CLK_UHC		17
#define JZ4770_CLK_GPU		18
#define JZ4770_CLK_BCH		19
#define JZ4770_CLK_LPCLK_MUX	20
#define JZ4770_CLK_GPS		21
#define JZ4770_CLK_SSI_MUX	22
#define JZ4770_CLK_PCM_MUX	23
#define JZ4770_CLK_I2S		24
#define JZ4770_CLK_OTG		25
#define JZ4770_CLK_SSI0		26
#define JZ4770_CLK_SSI1		27
#define JZ4770_CLK_SSI2		28
#define JZ4770_CLK_PCM0		29
#define JZ4770_CLK_PCM1		30
#define JZ4770_CLK_DMA		31
#define JZ4770_CLK_I2C0		32
#define JZ4770_CLK_I2C1		33
#define JZ4770_CLK_I2C2		34
#define JZ4770_CLK_UART0	35
#define JZ4770_CLK_UART1	36
#define JZ4770_CLK_UART2	37
#define JZ4770_CLK_UART3	38
#define JZ4770_CLK_IPU		39
#define JZ4770_CLK_ADC		40
#define JZ4770_CLK_AIC		41
#define JZ4770_CLK_AUX		42
#define JZ4770_CLK_VPU		43
#define JZ4770_CLK_UHC_PHY	44
#define JZ4770_CLK_OTG_PHY	45
#define JZ4770_CLK_EXT512	46
#define JZ4770_CLK_RTC		47

#endif /* __DT_BINDINGS_CLOCK_JZ4770_CGU_H__ */
