/*
 * This header provides constants for DRA7 ATL (Audio Tracking Logic)
 *
 * The constants defined in this header are used in dts files
 *
 * Copyright (C) 2013 Texas Instruments, Inc.
 *
 * <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 * This program is distributed "as is" WITHOUT ANY WARRANTY of any
 * kind, whether express or implied; without even the implied warranty
 * of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

#ifndef _DT_BINDINGS_CLK_DRA7_ATL_H
#define _DT_BINDINGS_CLK_DRA7_ATL_H

#define DRA7_ATL_WS_MCASP1_FSR		0
#define DRA7_ATL_WS_MCASP1_FSX		1
#define DRA7_ATL_WS_MCASP2_FSR		2
#define DRA7_ATL_WS_MCASP2_FSX		3
#define DRA7_ATL_WS_MCASP3_FSX		4
#define DRA7_ATL_WS_MCASP4_FSX		5
#define DRA7_ATL_WS_MCASP5_FSX		6
#define DRA7_ATL_WS_MCASP6_FSX		7
#define DRA7_ATL_WS_MCASP7_FSX		8
#define DRA7_ATL_WS_MCASP8_FSX		9
#define DRA7_ATL_WS_MCASP8_AHCLKX	10
#define DRA7_ATL_WS_XREF_CLK3		11
#define DRA7_ATL_WS_XREF_CLK0		12
#define DRA7_ATL_WS_XREF_CLK1		13
#define DRA7_ATL_WS_XREF_CLK2		14
#define DRA7_ATL_WS_OSC1_X1		15

#endif
