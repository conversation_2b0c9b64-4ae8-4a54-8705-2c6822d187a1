/* SPDX-License-Identifier: GPL-2.0 */

#ifndef _DT_BINDINGS_IIO_ADC_INGENIC_ADC_H
#define _DT_BINDINGS_IIO_ADC_INGENIC_ADC_H

/* ADC channel idx. */
#define INGENIC_ADC_AUX		0
#define INGENIC_ADC_BATTERY	1
#define INGENIC_ADC_AUX2	2
#define INGENIC_ADC_TOUCH_XP	3
#define INGENIC_ADC_TOUCH_YP	4
#define INGENIC_ADC_TOUCH_XN	5
#define INGENIC_ADC_TOUCH_YN	6
#define INGENIC_ADC_TOUCH_XD	7
#define INGENIC_ADC_TOUCH_YD	8

#endif
