/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Copyright (c) 2020 Rockchip Electronics Co. Ltd.
 *
 * Author: Wyon Bi <<EMAIL>>
 */

#ifndef _RK628_RGU_H
#define _RK628_RGU_H

#define RGU_LOGIC	0
#define RGU_CRU		1
#define RGU_REGFILE	2
#define RGU_I2C2APB	3
#define RGU_EFUSE	4
#define RGU_ADAPTER	5
#define RGU_CLK_RX	6
#define RGU_BT1120DEC	7
#define RGU_VOP		8
#define RGU_GPIO0	9
#define RGU_GPIO1	10
#define RGU_GPIO2	11
#define RGU_GPIO3	12
#define RGU_GPIO_DB0	13
#define RGU_GPIO_DB1	14
#define RGU_GPIO_DB2	15
#define RGU_GPIO_DB3	16
#define RGU_RXPHY	17
#define RGU_HDMIRX	18
#define RGU_TXPHY_CON	19
#define RGU_HDMITX	20
#define RGU_GVIHOST	21
#define RGU_DSI0	22
#define RGU_DSI1	23
#define RGU_CSI		24
#define RGU_TXDATA	25
#define RGU_DECODER	26
#define RGU_ENCODER	27
#define RGU_HDMIRX_PON	28
#define RGU_TXBYTEHS	29
#define RGU_TXESC	30

#endif
