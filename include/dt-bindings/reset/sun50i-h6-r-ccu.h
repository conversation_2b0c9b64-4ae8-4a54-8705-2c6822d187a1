/* SPDX-License-Identifier: (GPL-2.0+ or MIT) */
/*
 * Copyright (C) 2016 Icenowy Zheng <<EMAIL>>
 */

#ifndef _DT_BINDINGS_RST_SUN50I_H6_R_CCU_H_
#define _DT_BINDINGS_RST_SUN50I_H6_R_CCU_H_

#define RST_R_APB1_TIMER	0
#define RST_R_APB1_TWD		1
#define RST_R_APB1_PWM		2
#define RST_R_APB2_UART		3
#define RST_R_APB2_I2C		4
#define RST_R_APB1_IR		5
#define RST_R_APB1_W1		6

#endif /* _DT_BINDINGS_RST_SUN50I_H6_R_CCU_H_ */
