/* SPDX-License-Identifier: (GPL-2.0+ OR MIT) */
#ifndef __DT_BINDINGS_POWER_RK3588_POWER_H__
#define __DT_BINDINGS_POWER_RK3588_POWER_H__

/* VD_LITDSU */
#define RK3588_PD_CPU_0		0
#define RK3588_PD_CPU_1		1
#define RK3588_PD_CPU_2		2
#define RK3588_PD_CPU_3		3

/* VD_BIGCORE0 */
#define RK3588_PD_CPU_4		4
#define RK3588_PD_CPU_5		5

/* VD_BIGCORE1 */
#define RK3588_PD_CPU_6		6
#define RK3588_PD_CPU_7		7

/* VD_NPU */
#define RK3588_PD_NPU		8
#define RK3588_PD_NPUTOP	9
#define RK3588_PD_NPU1		10
#define RK3588_PD_NPU2		11

/* VD_GPU */
#define RK3588_PD_GPU		12

/* VD_VCODEC */
#define RK3588_PD_VCODEC	13
#define RK3588_PD_RKVDEC0	14
#define RK3588_PD_RKVDEC1	15
#define RK3588_PD_VENC0		16
#define RK3588_PD_VENC1		17

/* VD_DD01 */
#define RK3588_PD_DDR01		18

/* VD_DD23 */
#define RK3588_PD_DDR23		19

/* VD_LOGIC */
#define RK3588_PD_CENTER	20
#define RK3588_PD_VDPU		21
#define RK3588_PD_RGA30		22
#define RK3588_PD_AV1		23
#define RK3588_PD_VOP		24
#define RK3588_PD_VO0		25
#define RK3588_PD_VO1		26
#define RK3588_PD_VI		27
#define RK3588_PD_ISP1		28
#define RK3588_PD_FEC		29
#define RK3588_PD_RGA31		30
#define RK3588_PD_USB		31
#define RK3588_PD_PHP		32
#define RK3588_PD_GMAC		33
#define RK3588_PD_PCIE		34
#define RK3588_PD_NVM		35
#define RK3588_PD_NVM0		36
#define RK3588_PD_SDIO		37
#define RK3588_PD_AUDIO		38
#define RK3588_PD_SECURE	39
#define RK3588_PD_SDMMC		40
#define RK3588_PD_CRYPTO	41
#define RK3588_PD_BUS		42

/* VD_PMU */
#define RK3588_PD_PMU1		43

#endif
