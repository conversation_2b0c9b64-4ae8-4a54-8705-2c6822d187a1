/* SPDX-License-Identifier: (GPL-2.0 OR MIT) */
/*
 *  Copyright (C) 2018 <PERSON><PERSON><PERSON><PERSON>, <PERSON> <<EMAIL>>
 */

#ifndef __DT_BINDINGS_IMX8MQ_POWER_H__
#define __DT_BINDINGS_IMX8MQ_POWER_H__

#define IMX8M_POWER_DOMAIN_MIPI		0
#define IMX8M_POWER_DOMAIN_PCIE1	1
#define IMX8M_POWER_DOMAIN_USB_OTG1	2
#define IMX8M_POWER_DOMAIN_USB_OTG2	3
#define IMX8M_POWER_DOMAIN_DDR1		4
#define IMX8M_POWER_DOMAIN_GPU		5
#define IMX8M_POWER_DOMAIN_VPU		6
#define IMX8M_POWER_DOMAIN_DISP		7
#define IMX8M_POWER_DOMAIN_MIPI_CSI1	8
#define IMX8M_POWER_DOMAIN_MIPI_CSI2	9
#define IMX8M_POWER_DOMAIN_PCIE2	10

#endif
